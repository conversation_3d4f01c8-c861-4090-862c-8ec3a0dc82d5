﻿// <auto-generated />
using System;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace SFDSystem.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250630154639_UpdateExistingProjectsWithDays")]
    partial class UpdateExistingProjectsWithDays
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.6");

            modelBuilder.Entity("DriverManagementSystem.Models.ContractTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ContractIntroduction")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("DurationTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstPartyTemplate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("ObligationsTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("OwnershipTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PriceTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PurposeTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("SecondPartyTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleSpecsTemplate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ContractTemplates");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Driver", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CardIssueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("CardIssuePlace")
                        .HasColumnType("TEXT");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(30)
                        .HasColumnType("TEXT");

                    b.Property<string>("CardType")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("DriverCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<bool>("HasQuote")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LicenseIssueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("LicenseNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("QuoteDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("QuoteNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int?>("QuotedDays")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("QuotedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SectorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SectorName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleCapacity")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleColor")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleModel")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DriverCode")
                        .IsUnique();

                    b.ToTable("Drivers");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.DriverQuote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("DriverCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int>("DriverId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("DriverName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("QuoteDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<int>("QuotedDays")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("QuotedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("VehicleNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DriverId")
                        .HasDatabaseName("IX_DriverQuote_DriverId");

                    b.HasIndex("QuoteDate")
                        .HasDatabaseName("IX_DriverQuote_QuoteDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_DriverQuote_Status");

                    b.ToTable("DriverQuotes");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AddDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ApprovalBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DaysCount")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DepartureDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("DriverContract")
                        .HasColumnType("TEXT");

                    b.Property<string>("HijriDate")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsSelected")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MissionPurpose")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("OdkVisitNumber")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectsCount")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ReturnDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("SectorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SectorName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SecurityRoute")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("SelectedDrivers")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SubmissionTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("VisitorsCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("WinnerDriverMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("VisitNumber")
                        .IsUnique();

                    b.ToTable("FieldVisits");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitItinerary", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("تاريخ إنشاء السجل");

                    b.Property<int>("DayNumber")
                        .HasColumnType("INTEGER")
                        .HasComment("رقم اليوم في خط السير");

                    b.Property<int>("FieldVisitId")
                        .HasColumnType("INTEGER")
                        .HasComment("معرف الزيارة الميدانية");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true)
                        .HasComment("حالة السجل");

                    b.Property<string>("ItineraryText")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("تفاصيل خط السير لهذا اليوم");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("ملاحظات إضافية");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasComment("تاريخ آخر تحديث");

                    b.HasKey("Id");

                    b.HasIndex("FieldVisitId")
                        .HasDatabaseName("IX_FieldVisitItinerary_FieldVisitId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_FieldVisitItinerary_IsActive");

                    b.HasIndex("FieldVisitId", "DayNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_FieldVisitItinerary_FieldVisitId_DayNumber");

                    b.ToTable("FieldVisitItineraries");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<int>("FieldVisitId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectDays")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProjectNumber")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("FieldVisitId");

                    b.HasIndex("ProjectId");

                    b.ToTable("FieldVisitProjects");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("FieldVisitId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OfficerCode")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int>("OfficerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OfficerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OfficerRank")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("FieldVisitId");

                    b.ToTable("FieldVisitors");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.MessageAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileType")
                        .HasColumnType("TEXT");

                    b.Property<int>("MessageDocumentationId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MessageDocumentationId");

                    b.ToTable("MessageAttachments");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.MessageDocumentation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DocumentationDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstOfficer")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath1")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath2")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath3")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath4")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath5")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImagePath6")
                        .HasColumnType("TEXT");

                    b.Property<int>("ImagesCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ImagesFolderPath")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("ReportNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("SecondOfficer")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .HasColumnType("TEXT");

                    b.Property<string>("ThirdOfficer")
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitConductor1")
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitConductor2")
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitConductor3")
                        .HasColumnType("TEXT");

                    b.Property<string>("VisitNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("MessageDocumentations");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Officer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CardNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("CardType")
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("Rank")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("SectorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SectorName")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SectorId");

                    b.ToTable("Officers");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.ProfessionalTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastUsedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("UsageCount")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("ProfessionalTemplates");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectDays")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProjectNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProjectNumber")
                        .IsUnique();

                    b.ToTable("Projects");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Sector", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Sectors");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImagePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Role")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("User");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("UserId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.UserPermission", b =>
                {
                    b.Property<int>("PermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<bool>("IsGranted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<string>("PermissionDescription")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PermissionName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("PermissionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserPermissions");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Vehicle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Brand")
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .HasColumnType("TEXT");

                    b.Property<string>("Color")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DriverCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("IdCardNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LicenseNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .HasColumnType("TEXT");

                    b.Property<string>("OwnerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int>("SectorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SectorName")
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("Year")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitItinerary", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.FieldVisit", "FieldVisit")
                        .WithMany()
                        .HasForeignKey("FieldVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FieldVisit");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitProject", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.FieldVisit", "FieldVisit")
                        .WithMany("Projects")
                        .HasForeignKey("FieldVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DriverManagementSystem.Models.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("FieldVisit");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisitor", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.FieldVisit", null)
                        .WithMany("Visitors")
                        .HasForeignKey("FieldVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DriverManagementSystem.Models.MessageAttachment", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.MessageDocumentation", "MessageDocumentation")
                        .WithMany("Attachments")
                        .HasForeignKey("MessageDocumentationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MessageDocumentation");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.Officer", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.Sector", null)
                        .WithMany()
                        .HasForeignKey("SectorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("DriverManagementSystem.Models.UserPermission", b =>
                {
                    b.HasOne("DriverManagementSystem.Models.User", "User")
                        .WithMany("UserPermissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.FieldVisit", b =>
                {
                    b.Navigation("Projects");

                    b.Navigation("Visitors");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.MessageDocumentation", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("DriverManagementSystem.Models.User", b =>
                {
                    b.Navigation("UserPermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
