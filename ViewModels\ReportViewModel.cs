using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Documents;
using System.Printing;
using Microsoft.Win32;
using SFDSystem.Helpers;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;
using DriverManagementSystem.Helpers;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.ViewModels
{
    public class ReportViewModel : BindableBase
    {
        private readonly SqliteDataService _databaseService;
        private readonly DriverDataService _driverDataService;
        private readonly ApplicationDbContext _context;
        private ReportModel _reportData;
        private ObservableCollection<FieldVisit> _fieldVisits;
        private ContractTemplate _contractTemplate;
        private FieldVisit _selectedVisit;

        public ReportViewModel()
        {
            _databaseService = new SqliteDataService();
            _context = new ApplicationDbContext();
            _driverDataService = new DriverDataService(_context);
            _reportData = new ReportModel();
            _fieldVisits = new ObservableCollection<FieldVisit>();

            // Commands
            LoadDataCommand = new DelegateCommand(LoadData);
            GenerateReportCommand = new DelegateCommand(GenerateReport, CanGenerateReport);
            PrintReportCommand = new DelegateCommand(PrintReport, CanPrintReport);
            ExportToPdfCommand = new DelegateCommand(ExportToPdf, CanExportToPdf);


            LoadData();
            LoadContractTemplate();

            System.Diagnostics.Debug.WriteLine("🔧 تم إنشاء ReportViewModel جديد");
        }

        #region Properties

        public ReportModel ReportData
        {
            get => _reportData;
            set
            {
                SetProperty(ref _reportData, value);
                // تحديث النصوص المعالجة عند تغيير بيانات التقرير
                UpdateProcessedTexts();
            }
        }

        public ObservableCollection<FieldVisit> FieldVisits
        {
            get => _fieldVisits;
            set => SetProperty(ref _fieldVisits, value);
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set
            {
                if (SetProperty(ref _selectedVisit, value))
                {
                    GenerateReportCommand.RaiseCanExecuteChanged();
                    if (value != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 تم اختيار الزيارة: {value.VisitNumber} (ID: {value.Id})");
                        System.Diagnostics.Debug.WriteLine($"🔍 المشاريع في الزيارة المختارة: {value.Projects?.Count ?? 0}");
                        if (value.Projects?.Any() == true)
                        {
                            for (int i = 0; i < value.Projects.Count; i++)
                            {
                                var project = value.Projects[i];
                                System.Diagnostics.Debug.WriteLine($"🔍 المشروع {i + 1}: رقم='{project.ProjectNumber}', اسم='{project.ProjectName}'");
                            }
                        }
                        LoadVisitData(value);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("🔍 تم إلغاء اختيار الزيارة");
                        ClearReportData();
                        RaisePropertyChanged(nameof(ReportData));
                    }
                }
            }
        }

        // رقم الزيارة ODK
        private string _odkVisitNumber = string.Empty;
        public string OdkVisitNumber
        {
            get => _odkVisitNumber;
            set => SetProperty(ref _odkVisitNumber, value);
        }

        public ContractTemplate ContractTemplate
        {
            get => _contractTemplate;
            set
            {
                _contractTemplate = value;
                RaisePropertyChanged();
                // تحديث النصوص المعالجة عند تغيير القالب
                UpdateProcessedTexts();
            }
        }



        #endregion

        #region Commands

        public DelegateCommand LoadDataCommand { get; }
        public DelegateCommand GenerateReportCommand { get; }
        public DelegateCommand PrintReportCommand { get; }
        public DelegateCommand ExportToPdfCommand { get; }


        #endregion

        #region Methods

        private async void LoadContractTemplate()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();
                ContractTemplate = await _context.ContractTemplates.FirstOrDefaultAsync();

                if (ContractTemplate == null)
                {
                    // إنشاء قالب افتراضي
                    ContractTemplate = new ContractTemplate();
                    _context.ContractTemplates.Add(ContractTemplate);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب عقد افتراضي في ReportViewModel");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل قالب العقد في ReportViewModel (ID: {ContractTemplate.Id})");
                }

                // حفظ النصوص الأصلية عند التحميل
                SaveOriginalTexts();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل قالب العقد في ReportViewModel: {ex.Message}");
                // استخدام قالب افتراضي في حالة الخطأ
                ContractTemplate = new ContractTemplate();
                SaveOriginalTexts();
            }
        }

        private void SaveOriginalTexts()
        {
            if (ContractTemplate != null)
            {
                _originalContractIntroduction = ContractTemplate.ContractIntroduction;
                _originalFirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
                _originalSecondPartyTemplate = ContractTemplate.SecondPartyTemplate;

                System.Diagnostics.Debug.WriteLine("💾 تم حفظ النصوص الأصلية");
            }
        }

        // خصائص للنصوص الأصلية (قبل المعالجة)
        private string _originalContractIntroduction;
        private string _originalFirstPartyTemplate;
        private string _originalSecondPartyTemplate;

        private async void UpdateProcessedTexts()
        {
            if (ContractTemplate == null || ReportData == null)
                return;

            try
            {
                // التأكد من وجود النصوص الأصلية
                EnsureOriginalTextsExist();

                // معالجة النصوص الأصلية وتحديث القالب
                ContractTemplate.ContractIntroduction = ProcessContractText(_originalContractIntroduction);
                ContractTemplate.FirstPartyTemplate = ProcessContractText(_originalFirstPartyTemplate);
                ContractTemplate.SecondPartyTemplate = ProcessContractText(_originalSecondPartyTemplate);

                // حفظ النصوص المحدثة في قاعدة البيانات
                await SaveUpdatedTextsToDatabase();

                // إشعار الواجهة بالتحديث
                RaisePropertyChanged(nameof(ContractTemplate));

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث النصوص المعالجة وحفظها");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة النصوص: {ex.Message}");
            }
        }

        private async Task SaveUpdatedTextsToDatabase()
        {
            try
            {
                using var context = new Data.ApplicationDbContext();
                var existingTemplate = await context.ContractTemplates.FirstOrDefaultAsync();

                if (existingTemplate != null)
                {
                    // تحديث النصوص المعالجة في قاعدة البيانات
                    existingTemplate.ContractIntroduction = ContractTemplate.ContractIntroduction;
                    existingTemplate.FirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
                    existingTemplate.SecondPartyTemplate = ContractTemplate.SecondPartyTemplate;
                    existingTemplate.LastModified = DateTime.Now;

                    context.ContractTemplates.Update(existingTemplate);
                    await context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine("💾 تم حفظ النصوص المحدثة في قاعدة البيانات");
                    System.Diagnostics.Debug.WriteLine($"📝 النص المحدث: {ContractTemplate.ContractIntroduction.Substring(0, Math.Min(100, ContractTemplate.ContractIntroduction.Length))}...");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على قالب العقد في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ النصوص المحدثة: {ex.Message}");
            }
        }

        private async Task ReloadContractTemplate()
        {
            try
            {
                using var context = new Data.ApplicationDbContext();
                var template = await context.ContractTemplates.FirstOrDefaultAsync();

                if (template != null)
                {
                    // إعادة تحميل النصوص الأصلية من قاعدة البيانات
                    _originalContractIntroduction = template.ContractIntroduction;
                    _originalFirstPartyTemplate = template.FirstPartyTemplate;
                    _originalSecondPartyTemplate = template.SecondPartyTemplate;

                    // تحديث القالب الحالي
                    ContractTemplate.ContractIntroduction = template.ContractIntroduction;
                    ContractTemplate.FirstPartyTemplate = template.FirstPartyTemplate;
                    ContractTemplate.SecondPartyTemplate = template.SecondPartyTemplate;

                    System.Diagnostics.Debug.WriteLine("🔄 تم إعادة تحميل قالب العقد من قاعدة البيانات");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على قالب العقد في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تحميل قالب العقد: {ex.Message}");
            }
        }

        private void EnsureOriginalTextsExist()
        {
            // إذا لم تكن النصوص الأصلية محفوظة، احفظها الآن
            if (string.IsNullOrEmpty(_originalContractIntroduction))
            {
                _originalContractIntroduction = ContractTemplate.ContractIntroduction;
            }
            if (string.IsNullOrEmpty(_originalFirstPartyTemplate))
            {
                _originalFirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
            }
            if (string.IsNullOrEmpty(_originalSecondPartyTemplate))
            {
                _originalSecondPartyTemplate = ContractTemplate.SecondPartyTemplate;
            }
        }

        public void RestoreOriginalTexts()
        {
            if (ContractTemplate != null)
            {
                ContractTemplate.ContractIntroduction = _originalContractIntroduction ?? ContractTemplate.ContractIntroduction;
                ContractTemplate.FirstPartyTemplate = _originalFirstPartyTemplate ?? ContractTemplate.FirstPartyTemplate;
                ContractTemplate.SecondPartyTemplate = _originalSecondPartyTemplate ?? ContractTemplate.SecondPartyTemplate;

                RaisePropertyChanged(nameof(ContractTemplate));
                System.Diagnostics.Debug.WriteLine("🔄 تم استعادة النصوص الأصلية");
            }
        }

        private string ProcessContractText(string template)
        {
            if (string.IsNullOrEmpty(template) || ReportData == null)
                return template ?? "";

            var processedText = template;

            try
            {
                // استخدام تاريخ الإضافة من الزيارة المحددة كمصدر أساسي
                DateTime contractDate = DateTime.Now; // القيمة الافتراضية

                // البحث عن الزيارة المحددة للحصول على تاريخ الإضافة
                if (!string.IsNullOrEmpty(ReportData.VisitNumber))
                {
                    try
                    {
                        using var context = new Data.ApplicationDbContext();
                        var visit = context.FieldVisits.FirstOrDefault(v => v.VisitNumber == ReportData.VisitNumber);
                        if (visit != null)
                        {
                            contractDate = visit.AddDate; // استخدام تاريخ الإضافة من الزيارة
                            System.Diagnostics.Debug.WriteLine($"📅 تم استخدام تاريخ الإضافة: {contractDate:dd/MM/yyyy} للزيارة رقم: {ReportData.VisitNumber}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب تاريخ الإضافة: {ex.Message}");
                    }
                }

                // تحويل التاريخ إلى نص
                var contractDateString = contractDate.ToString("dd/MM/yyyy");
                var contractDayName = DateHelper.GetArabicDayName(contractDate);

                // معالجة التواريخ الأخرى
                var startDate = ReportData.DepartureDate ?? DateTime.Now.ToString("dd/MM/yyyy");
                var endDate = ReportData.ReturnDate ?? DateTime.Now.ToString("dd/MM/yyyy");
                var startDayName = DateHelper.GetArabicDayName(startDate);
                var endDayName = DateHelper.GetArabicDayName(endDate);

                // استبدال placeholders بالبيانات الفعلية
                processedText = processedText.Replace("{StartDate}", startDate);
                processedText = processedText.Replace("{EndDate}", endDate);
                processedText = processedText.Replace("{StartDateArabic}", startDayName);
                processedText = processedText.Replace("{EndDateArabic}", endDayName);

                // استبدال تاريخ العقد (تاريخ الإضافة)
                processedText = processedText.Replace("{ContractDate}", contractDateString);
                processedText = processedText.Replace("{ContractDateArabic}", contractDayName);

                // استبدال البيانات من ReportData
                processedText = processedText.Replace("{ReportData.ContractDate}", ReportData.ContractDate);
                processedText = processedText.Replace("{ReportData.ContractDayName}", ReportData.ContractDayName);
                processedText = processedText.Replace("{ReportData.StartDateArabic}", ReportData.StartDateArabic);
                processedText = processedText.Replace("{ReportData.EndDateArabic}", ReportData.EndDateArabic);

                // معالجة النص الكامل للتاريخ مع اليوم (استخدام تاريخ الإضافة)
                if (processedText.Contains("أنه في يوم"))
                {
                    var fullDateText = DateHelper.GetFullDateText(contractDate);

                    // استبدال جميع أشكال النص
                    processedText = processedText.Replace("أنه في يوم {StartDateArabic} الموافق {StartDate}", fullDateText);
                    processedText = processedText.Replace("أنه في يوم {ContractDateArabic} الموافق {ContractDate}", fullDateText);

                    // استبدال النص حتى لو كان بدون placeholders
                    if (processedText.Contains("أنه في يوم") && processedText.Contains("الموافق"))
                    {
                        // البحث عن النمط وتبديله
                        var pattern = @"أنه في يوم [^الموافق]+ الموافق \d{2}/\d{2}/\d{4}";
                        processedText = System.Text.RegularExpressions.Regex.Replace(processedText, pattern, fullDateText);
                    }

                    System.Diagnostics.Debug.WriteLine($"📅 تم تحديث النص إلى: {fullDateText}");
                }

                // بيانات السائق الفائز
                if (ReportData.WinnerDriver != null)
                {
                    processedText = processedText.Replace("{DriverName}", ReportData.WinnerDriver.DriverName ?? "");
                    processedText = processedText.Replace("{NationalId}", ReportData.WinnerDriver.NationalId ?? "");
                    processedText = processedText.Replace("{IssuePlace}", ReportData.WinnerDriver.IssueLocation ?? "");
                    processedText = processedText.Replace("{IssueDate}", ReportData.WinnerDriver.IssueDate ?? "");
                }

                // بيانات أخرى
                processedText = processedText.Replace("{VisitNumber}", ReportData.VisitNumber ?? "");
                processedText = processedText.Replace("{DaysCount}", ReportData.DaysCount.ToString());

                return processedText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة النص: {ex.Message}");
                return template;
            }
        }

        private async void LoadData()
        {
            try
            {
                var visits = await _databaseService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits.OrderByDescending(v => v.CreatedAt))
                {
                    FieldVisits.Add(visit);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadVisitData(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل بيانات الزيارة: {visit.VisitNumber} (ID: {visit.Id})");

                // مسح البيانات الافتراضية أولاً
                ClearReportData();

                // إجبار تحديث الواجهة فوراً
                RaisePropertyChanged(nameof(ReportData));

                // تحديث البيانات الأساسية من الزيارة المختارة
                ReportData.VisitNumber = visit.VisitNumber;
                ReportData.ReportDate = visit.AddDate.ToString("dd/MM/yyyy");
                ReportData.VisitNature = visit.MissionPurpose;
                ReportData.MissionPurpose = visit.MissionPurpose; // إضافة مهمة النزول للاستمارة
                ReportData.DepartureDate = visit.DepartureDate.ToString("dd/MM/yyyy");
                ReportData.ReturnDate = visit.ReturnDate.ToString("dd/MM/yyyy");
                ReportData.StartDate = visit.DepartureDate.ToString("dd/MM/yyyy"); // للاستمارة
                ReportData.EndDate = visit.ReturnDate.ToString("dd/MM/yyyy"); // للاستمارة
                // استخدم مجموع أيام المشاريع إذا كان متوفراً، وإلا استخدم DaysCount
                ReportData.DaysCount = visit.TotalProjectDays > 0 ? visit.TotalProjectDays : visit.DaysCount;
                ReportData.VisitorsCount = visit.VisitorsCount; // عدد القائمين بالزيارة
                ReportData.SectorName = visit.SectorName; // إضافة القطاع
                ReportData.Notes = visit.VisitNotes ?? "لا توجد ملاحظات إضافية"; // إضافة الملاحظات

                // الحقول الجديدة من Excel
                ReportData.ApprovalBy = visit.ApprovalBy ?? string.Empty;
                ReportData.SubmissionTime = visit.SubmissionTime;

                // تحديث رقم الزيارة ODK
                OdkVisitNumber = visit.OdkVisitNumber ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث رقم الزيارة ODK: '{OdkVisitNumber}'");

                // تنسيق وقت الإرسال
                if (visit.SubmissionTime.HasValue)
                {
                    ReportData.FormattedSubmissionTime = visit.SubmissionTime.Value.ToString("yyyy/MM/dd - HH:mm:ss");
                    System.Diagnostics.Debug.WriteLine($"✅ وقت الإرسال: {ReportData.FormattedSubmissionTime}");
                }

                // تحديث بيانات التاريخ واليوم للعقد
                ReportData.ContractDate = visit.AddDate.ToString("dd/MM/yyyy");
                ReportData.ContractDayName = Helpers.DateHelper.GetArabicDayName(visit.AddDate);
                ReportData.StartDateArabic = Helpers.DateHelper.GetArabicDayName(visit.DepartureDate);
                ReportData.EndDateArabic = Helpers.DateHelper.GetArabicDayName(visit.ReturnDate);

                System.Diagnostics.Debug.WriteLine($"✅ البيانات الأساسية: رقم={ReportData.VisitNumber}, تاريخ النزول={ReportData.DepartureDate}, الأيام={ReportData.DaysCount} (مشاريع: {visit.TotalProjectDays}, زيارة: {visit.DaysCount})");

                // تحميل القائمين بالزيارة من البيانات المحفوظة
                LoadVisitorsData(visit);
                System.Diagnostics.Debug.WriteLine($"✅ القائمين بالزيارة: {ReportData.VisitConductor}");

                // تحديث بيانات القائمين بالزيارة للاستمارة
                UpdateVisitorsForForm(visit);

                // تحديث النصوص المتغيرة بناءً على البيانات (بعد تحميل القائمين بالزيارة)
                UpdateDynamicTexts();

                // تحميل خط السير من البيانات المحفوظة
                LoadItineraryData(visit);
                System.Diagnostics.Debug.WriteLine($"✅ خط السير: {ReportData.RouteDescription}");

                // تحديث خط السير للاستمارة
                UpdateItineraryForForm(visit);

                // تحميل المشاريع من البيانات المحفوظة - مع تشخيص مفصل
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل المشاريع للزيارة {visit.VisitNumber}...");
                await LoadProjectsDataAsync(visit);
                System.Diagnostics.Debug.WriteLine($"✅ عدد المشاريع المحملة: {ReportData.Projects.Count}");

                // طباعة تفاصيل المشاريع المحملة
                for (int i = 0; i < ReportData.Projects.Count; i++)
                {
                    var project = ReportData.Projects[i];
                    System.Diagnostics.Debug.WriteLine($"📋 المشروع {i + 1}: {project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}");
                }

                // إجبار تحديث المشاريع في الواجهة
                RaisePropertyChanged(nameof(ReportData));

                // تحميل السائقين وعروض الأسعار المحفوظة
                await LoadDriversAndPricesData(visit);
                System.Diagnostics.Debug.WriteLine($"✅ عدد عروض الأسعار: {ReportData.PriceOffers.Count}");

                // تحميل بيانات السائق الفائز
                await LoadWinnerDriverData(visit);
                System.Diagnostics.Debug.WriteLine($"✅ السائق الفائز: {ReportData.WinnerDriver.DriverName}");

                // تحميل بيانات السيارة المختارة
                await LoadSelectedVehicleData(visit);
                System.Diagnostics.Debug.WriteLine($"✅ السائق المختار: {ReportData.SelectedDriverName}");

                // تحديث بيانات التوقيعات
                LoadSignaturesData(visit);

                // تحديث نص الرسالة بناءً على البيانات المحملة
                GenerateMessageText();

                // تحميل نص الرسالة للسائق الفائز
                LoadWinnerDriverMessage(visit);

                // تحميل صور التوثيق
                await LoadDocumentationImages(visit);

                System.Diagnostics.Debug.WriteLine("🎯 تم الانتهاء من تحميل جميع البيانات");

                // إعادة تحميل قالب العقد من قاعدة البيانات
                await ReloadContractTemplate();

                // تحديث النصوص المعالجة للعقد بالتاريخ الجديد
                UpdateProcessedTexts();

                // تحديث نهائي للواجهة
                RaisePropertyChanged(nameof(ReportData));

                // تحديث حالة الأزرار
                GenerateReportCommand.RaiseCanExecuteChanged();
                PrintReportCommand.RaiseCanExecuteChanged();
                ExportToPdfCommand.RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الزيارة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                MessageBox.Show($"خطأ في تحميل بيانات الزيارة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearReportData()
        {
            ReportData.VisitNumber = string.Empty;
            ReportData.ReportDate = string.Empty;
            ReportData.VisitNature = string.Empty;
            ReportData.VisitConductor = string.Empty;
            ReportData.VisitConductorWithRank = string.Empty;
            ReportData.RouteDescription = string.Empty;
            ReportData.DepartureDate = string.Empty;
            ReportData.ReturnDate = string.Empty;
            ReportData.DaysCount = 0;
            ReportData.SectorName = string.Empty; // إضافة القطاع
            ReportData.Notes = string.Empty; // إضافة الملاحظات

            // مسح رقم الزيارة ODK
            OdkVisitNumber = string.Empty;

            // مسح بيانات التاريخ واليوم
            ReportData.ContractDate = string.Empty;
            ReportData.ContractDayName = string.Empty;
            ReportData.StartDateArabic = string.Empty;
            ReportData.EndDateArabic = string.Empty;

            // مسح مسارات صور التوثيق
            ReportData.DocumentationImage1 = string.Empty;
            ReportData.DocumentationImage2 = string.Empty;
            ReportData.DocumentationImage3 = string.Empty;
            ReportData.DocumentationImage4 = string.Empty;

            // مسح مسارات صور التوثيق
            ReportData.DocumentationImage1 = string.Empty;
            ReportData.DocumentationImage2 = string.Empty;
            ReportData.DocumentationImage3 = string.Empty;
            ReportData.DocumentationImage4 = string.Empty;

            // مسح مسارات صور التوثيق
            ReportData.DocumentationImage1 = string.Empty;
            ReportData.DocumentationImage2 = string.Empty;
            ReportData.DocumentationImage3 = string.Empty;
            ReportData.DocumentationImage4 = string.Empty;

            ReportData.Projects.Clear();
            ReportData.PriceOffers.Clear();
            ReportData.SelectedDriverName = string.Empty;
            ReportData.SelectedDriverPhone = string.Empty;
            ReportData.VehicleType = string.Empty;
            ReportData.VehicleModel = string.Empty;
            ReportData.VehicleColor = string.Empty;
            ReportData.PlateNumber = string.Empty;

            // استعادة النصوص الأصلية عند مسح البيانات
            RestoreOriginalTexts();
        }

        private void LoadVisitorsData(FieldVisit visit)
        {
            try
            {
                // جلب القائمين بالزيارة من البيانات المحفوظة
                if (visit.Visitors?.Any() == true)
                {
                    // إنشاء قائمة بدون صفة للتقارير
                    var visitorDetailsWithoutRank = visit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v =>
                        {
                            // تنسيق الاسم فقط بدون الصفة
                            var name = v.OfficerName ?? "غير محدد";
                            return name;
                        })
                        .ToList();

                    // إنشاء قائمة مع الصفة للعقد
                    var visitorDetailsWithRank = visit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v =>
                        {
                            // تنسيق الاسم مع الصفة للعقد
                            var name = v.OfficerName ?? "غير محدد";
                            var rank = v.OfficerRank ?? "";

                            if (!string.IsNullOrWhiteSpace(rank))
                            {
                                return $"{name} - {rank}";
                            }
                            return name;
                        })
                        .ToList();

                    if (visitorDetailsWithoutRank.Any())
                    {
                        // للتقارير والتوقيعات (بدون صفة)
                        ReportData.VisitConductor = string.Join(" & ", visitorDetailsWithoutRank);
                        // للعقد (مع الصفة)
                        ReportData.VisitConductorWithRank = string.Join(" & ", visitorDetailsWithRank);
                    }
                    else
                    {
                        ReportData.VisitConductor = "غير محدد";
                        ReportData.VisitConductorWithRank = "غير محدد";
                    }
                }
                else
                {
                    ReportData.VisitConductor = "غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.VisitConductor = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading visitors: {ex.Message}");
            }
        }

        private void LoadItineraryData(FieldVisit visit)
        {
            try
            {
                // جلب خط السير من البيانات المحفوظة
                if (visit.Itinerary?.Any() == true)
                {
                    var itineraryItems = visit.Itinerary
                        .Where(item => !string.IsNullOrWhiteSpace(item))
                        .ToList();

                    if (itineraryItems.Any())
                    {
                        // إضافة فاصل "//" بين الأيام لتوضيح الفصل بين كل يوم
                        ReportData.RouteDescription = string.Join(" // ", itineraryItems);
                    }
                    else
                    {
                        ReportData.RouteDescription = "خط السير غير محدد";
                    }
                }
                else
                {
                    ReportData.RouteDescription = "خط السير غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.RouteDescription = "خط السير غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading itinerary: {ex.Message}");
            }
        }

        private async Task LoadProjectsDataAsync(FieldVisit visit)
        {
            try
            {
                // مسح المشاريع الحالية
                ReportData.Projects.Clear();
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل المشاريع للزيارة: {visit.VisitNumber} (ID: {visit.Id})");

                // تحميل بيانات المشاريع الكاملة مع التركيز على عدد الأيام
                if (visit.Projects?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {visit.Projects.Count} مشروع في الزيارة");

                    // إضافة كل مشروع مع بياناته الكاملة
                    for (int i = 0; i < visit.Projects.Count; i++)
                    {
                        var project = visit.Projects[i];
                        System.Diagnostics.Debug.WriteLine($"🔍 المشروع {i + 1}: رقم='{project.ProjectNumber}', اسم='{project.ProjectName}', أيام={project.ProjectDays}");

                        // إضافة المشروع مع جميع البيانات
                        var reportItem = new ProjectReportItem
                        {
                            SerialNumber = i + 1,
                            ProjectNumber = !string.IsNullOrWhiteSpace(project.ProjectNumber) ? project.ProjectNumber : "-",
                            ProjectName = !string.IsNullOrWhiteSpace(project.ProjectName) ? project.ProjectName : "-",
                            ProjectDays = project.ProjectDays
                        };

                        ReportData.Projects.Add(reportItem);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشروع: {reportItem.SerialNumber} - {reportItem.ProjectNumber} - {reportItem.ProjectName} - {reportItem.ProjectDays} أيام");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع في الزيارة المختارة");

                    // إضافة رسالة توضيحية
                    ReportData.Projects.Add(new ProjectReportItem
                    {
                        SerialNumber = 1,
                        ProjectNumber = "لا توجد مشاريع",
                        ProjectName = "لم يتم تحديد مشاريع لهذه الزيارة",
                        ProjectDays = 0
                    });
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {ReportData.Projects.Count} عنصر مشروع بنجاح للتقرير");

                // طباعة تفاصيل المشاريع النهائية
                for (int i = 0; i < ReportData.Projects.Count; i++)
                {
                    var item = ReportData.Projects[i];
                    System.Diagnostics.Debug.WriteLine($"📋 المشروع النهائي {i + 1}: {item.SerialNumber} | {item.ProjectNumber} | {item.ProjectName}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المشاريع: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack trace: {ex.StackTrace}");

                // إضافة رسالة خطأ
                ReportData.Projects.Clear();
                ReportData.Projects.Add(new ProjectReportItem
                {
                    SerialNumber = 1,
                    ProjectNumber = "خطأ في التحميل",
                    ProjectName = $"خطأ في تحميل المشاريع: {ex.Message}"
                });
            }
        }

        public async Task LoadDriversAndPricesData(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل بيانات السائقين للزيارة: {visit.VisitNumber}");
                ReportData.PriceOffers.Clear();

                // أولاً: محاولة جلب البيانات من جدول DriverQuotes الجديد (الأولوية العليا)
                System.Diagnostics.Debug.WriteLine($"🔍 المحاولة الأولى: البحث في قاعدة البيانات...");
                var realOffers = await LoadRealOffersFromDatabase(visit.VisitNumber);

                if (realOffers?.Any() == true)
                {
                    foreach (var offer in realOffers)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {realOffers.Count} عرض سعر حقيقي من قاعدة البيانات");
                    return;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على عروض في قاعدة البيانات للزيارة {visit.VisitNumber}");
                }

                // ثانياً: محاولة جلب البيانات من نافذة الرسائل المهنية المفتوحة
                System.Diagnostics.Debug.WriteLine($"🔍 المحاولة الثانية: البحث في نافذة الرسائل المهنية...");
                var offersFromMessagesWindow = LoadOffersFromProfessionalMessagesWindow(visit.VisitNumber);
                if (offersFromMessagesWindow?.Any() == true)
                {
                    foreach (var offer in offersFromMessagesWindow)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offersFromMessagesWindow.Count} عرض سعر من نافذة الرسائل المهنية");
                    return;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على عروض في نافذة الرسائل المهنية");
                }

                // ثالثاً: محاولة جلب البيانات من حقل SelectedDrivers في الزيارة
                var offersFromVisit = LoadOffersFromVisitData(visit);

                if (offersFromVisit?.Any() == true)
                {
                    foreach (var offer in offersFromVisit)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offersFromVisit.Count} عرض سعر من بيانات الزيارة");
                    return;
                }

                // ثالثاً: محاولة جلب البيانات من جدول DriverQuotes القديم
                var priceOffers = await _driverDataService.GetEnhancedPriceOffersAsync(visit.VisitNumber);

                if (priceOffers?.Any() == true)
                {
                    foreach (var offer in priceOffers)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {priceOffers.Count} عرض سعر من جدول العروض القديم");
                    return;
                }

                // رابعاً: محاولة جلب البيانات من الطريقة القديمة
                var fallbackOffers = await _databaseService.GetPriceOffersByVisitIdAsync(visit.Id);

                if (fallbackOffers?.Any() == true)
                {
                    foreach (var offer in fallbackOffers)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {fallbackOffers.Count} عرض سعر من الطريقة البديلة");
                    return;
                }

                // خامساً: عرض رسالة أنه لا توجد عروض لهذه الزيارة
                System.Diagnostics.Debug.WriteLine($"⚠️ لم توجد عروض حقيقية للزيارة {visit.VisitNumber}");
                ReportData.PriceOffers.Add(new PriceOfferItem
                {
                    SerialNumber = 1,
                    DriverName = $"لا توجد عروض أسعار للزيارة {visit.VisitNumber}",
                    PhoneNumber = "---",
                    OfferedPrice = 0,
                    Status = "لم يتم تقديم عروض لهذه الزيارة",
                    IsWinner = false
                });
            }
            catch (Exception ex)
            {
                ReportData.PriceOffers.Clear();
                ReportData.PriceOffers.Add(new PriceOfferItem
                {
                    SerialNumber = 1,
                    DriverName = "خطأ في تحميل البيانات",
                    PhoneNumber = "غير محدد",
                    OfferedPrice = 0,
                    Status = "خطأ",
                    IsWinner = false
                });
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل عروض الأسعار: {ex.Message}");
            }
        }

        private async Task LoadSelectedVehicleData(FieldVisit visit)
        {
            try
            {
                // جلب بيانات السيارة المختارة من عروض الأسعار
                var offers = await _databaseService.GetVisitOffersAsync(visit.VisitNumber);
                var selectedOffer = offers?.FirstOrDefault(o => o.IsSelected);

                if (selectedOffer != null)
                {
                    ReportData.SelectedDriverName = selectedOffer.DriverName;
                    ReportData.SelectedDriverPhone = selectedOffer.PhoneNumber;
                    ReportData.VehicleType = selectedOffer.VehicleType ?? "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = selectedOffer.VehicleNumber ?? "غير محدد";
                }
                else if (ReportData.PriceOffers.Any() &&
                         ReportData.PriceOffers.First().DriverName != "لا توجد عروض أسعار")
                {
                    // استخدم أول عرض كسائق مختار
                    var firstOffer = ReportData.PriceOffers.First();
                    ReportData.SelectedDriverName = firstOffer.DriverName;
                    ReportData.SelectedDriverPhone = firstOffer.PhoneNumber;
                    ReportData.VehicleType = "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = "غير محدد";
                }
                else
                {
                    // لا توجد بيانات سائق
                    ReportData.SelectedDriverName = "لم يتم اختيار سائق";
                    ReportData.SelectedDriverPhone = "غير محدد";
                    ReportData.VehicleType = "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = "غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.SelectedDriverName = "خطأ في تحميل البيانات";
                ReportData.SelectedDriverPhone = "غير محدد";
                ReportData.VehicleType = "غير محدد";
                ReportData.VehicleModel = "غير محدد";
                ReportData.VehicleColor = "غير محدد";
                ReportData.PlateNumber = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading vehicle data: {ex.Message}");
            }
        }

        private void LoadSignaturesData(FieldVisit visit)
        {
            try
            {
                // بيانات التوقيعات - من بيانات النظام
                ReportData.TaskManagerName = "مسئول المهمة";
                ReportData.MovementResponsibleName = "مسئول الحركة";
                ReportData.BranchManagerName = "مدير الفرع";
                ReportData.BranchManagerTitle = "مدير الفرع";
            }
            catch (Exception ex)
            {
                ReportData.TaskManagerName = "غير محدد";
                ReportData.MovementResponsibleName = "غير محدد";
                ReportData.BranchManagerName = "غير محدد";
                ReportData.BranchManagerTitle = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading signatures: {ex.Message}");
            }
        }

        // تم إلغاء هذه الدالة - لا نريد بيانات تجريبية
        // private async void LoadSampleDriversAndPrices() { }

        private async void LoadWinnerDriverMessage(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن السائق الفائز للزيارة: {visit.VisitNumber}");

                // البحث عن السائق الفائز في قاعدة البيانات أولاً
                using var context = new Data.ApplicationDbContext();
                var offersService = new Services.OffersService(context);

                var savedOffers = await offersService.GetVisitOffersByNumberAsync(visit.VisitNumber);
                var winnerOffer = savedOffers?.FirstOrDefault(o => o.IsWinner);

                if (winnerOffer != null)
                {
                    // توليد نص الرسالة للسائق الفائز من قاعدة البيانات
                    var messageText = GenerateWinnerMessage(winnerOffer.DriverName, visit);
                    ReportData.WinnerDriverMessage = messageText;
                    System.Diagnostics.Debug.WriteLine($"✅ تم توليد نص الرسالة للسائق الفائز من قاعدة البيانات: {winnerOffer.DriverName}");
                    return;
                }

                // البحث عن السائق الفائز في بيانات العروض المحفوظة (النسخة الاحتياطية)
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(" | ");

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            var winnerName = parts[0];

                            // توليد نص الرسالة للسائق الفائز
                            var messageText = GenerateWinnerMessage(winnerName, visit);
                            ReportData.WinnerDriverMessage = messageText;

                            System.Diagnostics.Debug.WriteLine($"✅ تم توليد نص الرسالة للسائق الفائز من البيانات المحفوظة: {winnerName}");
                            return;
                        }
                    }
                }

                // إذا لم يتم العثور على سائق فائز
                ReportData.WinnerDriverMessage = "لم يتم تحديد السائق الفائز بعد";
                System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد سائق فائز محدد");
            }
            catch (Exception ex)
            {
                ReportData.WinnerDriverMessage = "خطأ في تحميل نص الرسالة";
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل نص الرسالة: {ex.Message}");
            }
        }

        private string GenerateWinnerMessage(string driverName, FieldVisit visit)
        {
            try
            {
                // بناء نص خط السير
                var itineraryText = "";
                if (visit.Itinerary?.Any() == true)
                {
                    itineraryText = string.Join(" - ", visit.Itinerary);
                }

                // بناء نص القائمين بالزيارة (الأسماء فقط بدون الرتب)
                var visitorsText = "";
                if (visit.Visitors?.Any() == true)
                {
                    visitorsText = string.Join(" و ", visit.Visitors.Select(v => v.OfficerName));
                }

                // حساب تواريخ النزول والعودة
                var startDate = visit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = visit.ReturnDate.ToString("dd/MM/yyyy");

                var daysToShow = visit.TotalProjectDays > 0 ? visit.TotalProjectDays : visit.DaysCount;
                var messageText = $@"الأخ/{driverName} المحترم،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({daysToShow} يوم) مع الأخ: {visitorsText}
في المناطق التالية:
{itineraryText}
📅 تاريخ النزول: {startDate} - 📅 تاريخ العودة: {endDate}
وشكراً - ادارة حركة السائقين بالصندوق الاجتماعي فرع ذمار";

                return messageText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد نص الرسالة: {ex.Message}");
                return $"الأخ/{driverName} المحترم، تم اختياركم للزيارة الميدانية رقم {visit.VisitNumber}";
            }
        }

        private void GenerateMessageText()
        {
            if (SelectedVisit == null) return;

            try
            {
                if (string.IsNullOrWhiteSpace(ReportData.SelectedDriverName) ||
                    ReportData.SelectedDriverName == "لم يتم اختيار سائق" ||
                    ReportData.SelectedDriverName == "خطأ في تحميل البيانات")
                {
                    ReportData.MessageText = $"تم استطلاع أسعار النقل للمشاريع المذكورة أعلاه لمدة {ReportData.DaysCount} يوم " +
                                           $"من تاريخ {ReportData.DepartureDate} إلى {ReportData.ReturnDate}. " +
                                           $"لم يتم اختيار سائق بعد.";
                }
                else
                {
                    ReportData.MessageText = $"تم استطلاع أسعار النقل للمشاريع المذكورة أعلاه لمدة {ReportData.DaysCount} يوم " +
                                           $"من تاريخ {ReportData.DepartureDate} إلى {ReportData.ReturnDate}، " +
                                           $"وقد تم اختيار السائق {ReportData.SelectedDriverName} " +
                                           $"برقم الهاتف {ReportData.SelectedDriverPhone}.";
                }
            }
            catch (Exception ex)
            {
                ReportData.MessageText = "خطأ في إنشاء نص الرسالة";
                System.Diagnostics.Debug.WriteLine($"Error generating message: {ex.Message}");
            }
        }

        private void GenerateReport()
        {
            try
            {
                if (SelectedVisit == null)
                {
                    MessageBox.Show("يرجى اختيار زيارة ميدانية أولاً", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

             

                PrintReportCommand.RaiseCanExecuteChanged();
                ExportToPdfCommand.RaiseCanExecuteChanged();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReport()
        {
            try
            {
                // البحث عن ReportView في النافذة الحالية
                var mainWindow = Application.Current.MainWindow;
                var reportView = FindReportView(mainWindow);

                if (reportView != null)
                {
                    var printDialog = new PrintDialog();

                    // عرض شاشة اختيار الطابعة للمستخدم
                    try
                    {
                        // عرض جميع الطابعات المثبتة للمستخدم ليختار
                        System.Diagnostics.Debug.WriteLine("🖨️ عرض شاشة اختيار الطابعة للمستخدم...");

                        // تحديد A4 مسبقاً
                        var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                        printDialog.PrintTicket.PageMediaSize = a4Size;
                        printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                        System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 للطابعة المختارة");
                    }
                    catch (Exception preEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد الطابعة: {preEx.Message}");
                    }

                    if (printDialog.ShowDialog() == true)
                    {
                        // تحديد إعدادات الطباعة A4 بقوة
                        try
                        {
                            // تحديد حجم الصفحة A4 بطرق متعددة لضمان التطبيق
                            if (printDialog.PrintTicket != null)
                            {
                                printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                                    System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                                printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                                // تأكيد إضافي لحجم A4
                                var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                                printDialog.PrintTicket.PageMediaSize = a4Size;

                                System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات الطباعة A4 بقوة");
                            }
                        }
                        catch (Exception pageEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات الطباعة: {pageEx.Message}");

                            // محاولة بديلة لتحديد A4
                            try
                            {
                                printDialog.PrintQueue.DefaultPrintTicket.PageMediaSize =
                                    new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                                System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 عبر PrintQueue");
                            }
                            catch (Exception fallbackEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في الطريقة البديلة: {fallbackEx.Message}");
                            }
                        }

                        // طباعة التقرير بصفحات منفصلة
                        PrintReportPages(printDialog, reportView);

                        // عرض رسالة تأكيد ديناميكية بناءً على عدد الصفحات المطبوعة
                        var reportPages = FindReportPages(reportView);
                        var pageCount = reportPages?.Count ?? 0;

                        string successMessage = $"تم إرسال جميع صفحات التقرير للطباعة بنجاح!\n\n📄 تم طباعة {pageCount} صفحات في مستند واحد:\n";

                        if (pageCount >= 4)
                        {
                            successMessage += "• الصفحة 1: محضر استدراج عروض الأسعار\n";
                            successMessage += "• الصفحة 2: عقد إيجار السيارة (الجزء الأول)\n";
                            successMessage += "• الصفحة 3: عقد إيجار السيارة (الجزء الثاني)\n";
                            successMessage += "• الصفحة 4: بيان وإقرار\n";
                            if (pageCount > 4)
                            {
                                successMessage += $"• صفحات إضافية: {pageCount - 4}\n";
                            }
                        }
                        else
                        {
                            successMessage += $"• جميع الصفحات المتاحة ({pageCount} صفحة)\n";
                        }

                        successMessage += "\n🖨️ تم إرسال جميع الصفحات للطابعة في مهمة طباعة واحدة.\n📐 حجم الطباعة: A4 (210×297 مم)\n📋 تحقق من طابعتك لاستلام المستندات.";

                        MessageBox.Show(successMessage, "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على التقرير للطباعة.\nتأكد من أنك في صفحة التقارير وأن التقرير معروض.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReportPages(PrintDialog printDialog, FrameworkElement reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء نظام الطباعة المحسن...");

                // إنشاء مستند طباعة شامل يحتوي على جميع الصفحات
                var printDocument = CreateCompletePrintDocument(reportView);

                if (printDocument != null)
                {
                    // طباعة المستند الكامل مرة واحدة
                    printDialog.PrintVisual(printDocument, "تقرير الزيارة الميدانية - مستند كامل");
                    System.Diagnostics.Debug.WriteLine("✅ تم إرسال المستند الكامل للطباعة بنجاح");
                }
                else
                {
                    // في حالة فشل إنشاء المستند الكامل، اطبع التقرير الأصلي
                    printDialog.PrintVisual(reportView, "تقرير الزيارة الميدانية");
                    System.Diagnostics.Debug.WriteLine("✅ تم طباعة التقرير الأصلي");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نظام الطباعة: {ex.Message}");

                // في حالة الخطأ، استخدم الطريقة الأساسية
                try
                {
                    printDialog.PrintVisual(reportView, "تقرير الزيارة الميدانية");
                    System.Diagnostics.Debug.WriteLine("✅ تم استخدام الطريقة الأساسية للطباعة");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في الطباعة الأساسية: {fallbackEx.Message}");
                    MessageBox.Show($"خطأ في الطباعة: {fallbackEx.Message}\n\nتأكد من أن الطابعة متصلة وتعمل بشكل صحيح.", "خطأ في الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private FrameworkElement CreateCompletePrintDocument(FrameworkElement reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند طباعة شامل...");

                // البحث عن جميع صفحات التقرير
                var reportPages = FindReportPages(reportView);

                if (reportPages?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {reportPages.Count} صفحة");

                    // إنشاء حاوي رئيسي لجميع الصفحات بحجم A4 محسن
                    var mainContainer = new StackPanel
                    {
                        Background = System.Windows.Media.Brushes.White,
                        Orientation = Orientation.Vertical,
                        Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                        HorizontalAlignment = HorizontalAlignment.Center,
                        FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي صحيح
                        Margin = new Thickness(0)
                    };

                    // إضافة كل صفحة إلى الحاوي الرئيسي
                    for (int i = 0; i < reportPages.Count; i++)
                    {
                        var page = reportPages[i];

                        // إنشاء نسخة محسنة من الصفحة للطباعة مع الحفاظ على البيانات والـ DataContext
                        var pageClone = CreateEnhancedPrintablePageWithDataContext(page);

                        if (pageClone != null)
                        {
                            // إضافة الصفحة إلى الحاوي
                            mainContainer.Children.Add(pageClone);

                            // إضافة فاصل صفحة إذا لم تكن الصفحة الأخيرة
                            if (i < reportPages.Count - 1)
                            {
                                var pageBreak = new Border
                                {
                                    Height = 50,
                                    Background = System.Windows.Media.Brushes.Transparent
                                };
                                mainContainer.Children.Add(pageBreak);
                            }
                        }
                    }

                    // تحديث تخطيط الحاوي
                    mainContainer.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    mainContainer.Arrange(new Rect(mainContainer.DesiredSize));
                    mainContainer.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند شامل يحتوي على {reportPages.Count} صفحة");
                    return mainContainer;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على صفحات منفصلة");
                    return reportView; // إرجاع التقرير الأصلي
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند الشامل: {ex.Message}");
                return null;
            }
        }

        private FrameworkElement ClonePageForPrint(Border originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة للطباعة
                var xaml = System.Windows.Markup.XamlWriter.Save(originalPage);
                var clonedPage = (FrameworkElement)System.Windows.Markup.XamlReader.Parse(xaml);

                // تطبيق خصائص الطباعة A4
                clonedPage.Width = 794;   // عرض A4 (210mm)
                clonedPage.Height = 1123; // ارتفاع A4 (297mm)
                clonedPage.HorizontalAlignment = HorizontalAlignment.Center;
                clonedPage.VerticalAlignment = VerticalAlignment.Top;

                // تحديث التخطيط
                clonedPage.Measure(new Size(794, 1123));
                clonedPage.Arrange(new Rect(0, 0, 794, 1123));
                clonedPage.UpdateLayout();

                System.Diagnostics.Debug.WriteLine($"✅ تم نسخ الصفحة بحجم A4: 794×1123");
                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في نسخ الصفحة: {ex.Message}");
                return originalPage; // إرجاع الصفحة الأصلية
            }
        }



        private List<Border> FindReportPages(FrameworkElement reportView)
        {
            var pages = new List<Border>();

            try
            {
                // البحث عن StackPanel الرئيسي
                var mainStackPanel = FindChild<StackPanel>(reportView);

                if (mainStackPanel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على StackPanel الرئيسي مع {mainStackPanel.Children.Count} عنصر فرعي");

                    // البحث عن جميع Border التي تحتوي على صفحات التقرير
                    foreach (var child in mainStackPanel.Children)
                    {
                        if (child is Border border)
                        {
                            // التحقق من وجود Style أو من خلال الحجم أو المحتوى
                            bool isReportPage = false;

                            // طريقة 1: التحقق من Style (PrintPageStyle)
                            if (border.Style != null)
                            {
                                // التحقق من اسم الـ Style في الموارد
                                try
                                {
                                    var printPageStyle = border.TryFindResource("PrintPageStyle");
                                    if (printPageStyle != null && border.Style == printPageStyle)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ PrintPageStyle");
                                    }
                                }
                                catch
                                {
                                    // في حالة فشل البحث عن الـ Style، استخدم طريقة أخرى
                                    var styleKey = border.Style.ToString();
                                    if (styleKey.Contains("PrintPageStyle"))
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ Style: {styleKey}");
                                    }
                                }
                            }

                            // طريقة 2: التحقق من خلال المحتوى (StackPanel مع محتوى كبير)
                            if (!isReportPage && border.Child is StackPanel stackPanel)
                            {
                                // التحقق من وجود محتوى كافي يشير إلى صفحة تقرير
                                if (stackPanel.Children.Count > 3)
                                {
                                    isReportPage = true;
                                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالمحتوى: {stackPanel.Children.Count} عناصر");
                                }
                            }

                            // طريقة 3: التحقق من خلال الهامش والخصائص
                            if (!isReportPage)
                            {
                                // التحقق من الهامش المميز للصفحات
                                var margin = border.Margin;
                                if ((margin.Top >= 5 && margin.Bottom >= 5) ||
                                    border.ActualWidth > 500 || border.ActualHeight > 600)
                                {
                                    // التأكد من أنه ليس فاصل بين الصفحات
                                    if (border.Height != 30 &&
                                        border.Background != System.Windows.Media.Brushes.Transparent)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالخصائص: Margin={margin}, Size={border.ActualWidth}x{border.ActualHeight}");
                                    }
                                }
                            }

                            if (isReportPage)
                            {
                                pages.Add(border);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"🎯 تم العثور على {pages.Count} صفحة تقرير");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن صفحات التقرير: {ex.Message}");
            }

            return pages;
        }

        private string GetPageTitle(int pageNumber)
        {
            return pageNumber switch
            {
                1 => "الصفحة 1 - محضر استدراج عروض الأسعار",
                2 => "الصفحة 2 - عقد إيجار السيارة (الجزء الأول)",
                3 => "الصفحة 3 - عقد إيجار السيارة (الجزء الثاني)",
                4 => "الصفحة 4 - بيان وإقرار",
                _ => $"الصفحة {pageNumber} - تقرير الزيارة الميدانية"
            };
        }

        private FrameworkElement CreatePrintablePage(Border originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة للطباعة
                var printContainer = new Grid
                {
                    Width = 794,  // عرض A4
                    Height = 1123, // ارتفاع A4
                    Background = Brushes.White
                };

                // نسخ محتوى الصفحة
                var pageClone = ClonePage(originalPage);
                printContainer.Children.Add(pageClone);

                // تحديث التخطيط
                printContainer.Measure(new Size(794, 1123));
                printContainer.Arrange(new Rect(0, 0, 794, 1123));
                printContainer.UpdateLayout();

                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحة للطباعة: {ex.Message}");
                return originalPage; // إرجاع الصفحة الأصلية في حالة الخطأ
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للطباعة مع الحفاظ على البيانات والتنسيق العربي والـ DataContext
        /// </summary>
        private FrameworkElement CreateEnhancedPrintablePageWithDataContext(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء نسخة محسنة من الصفحة للطباعة مع الـ DataContext...");

                // إنشاء حاوي للطباعة بحجم A4 محسن
                var printContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي
                    Margin = new Thickness(0),
                    DataContext = originalPage.DataContext // نسخ الـ DataContext
                };

                // إنشاء Border داخلي مع هوامش مناسبة
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft,
                    DataContext = originalPage.DataContext // نسخ الـ DataContext
                };

                // بدلاً من نسخ معقد، استخدم الصفحة الأصلية مباشرة مع تطبيق التحسينات
                if (originalPage.Parent is Panel parentPanel)
                {
                    parentPanel.Children.Remove(originalPage);
                }

                // تطبيق التحسينات على الصفحة الأصلية
                originalPage.FlowDirection = FlowDirection.RightToLeft;
                innerBorder.Child = originalPage;

                printContainer.Children.Add(innerBorder);

                // تحديث التخطيط بعناية
                printContainer.Measure(new Size(816, 1056));
                printContainer.Arrange(new Rect(0, 0, 816, 1056));
                printContainer.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة محسنة من الصفحة مع الـ DataContext بنجاح");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة المحسنة مع الـ DataContext: {ex.Message}");
                return CreateEnhancedPrintablePage(originalPage); // العودة للطريقة العادية
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للطباعة مع الحفاظ على البيانات والتنسيق العربي
        /// </summary>
        private FrameworkElement CreateEnhancedPrintablePage(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء نسخة محسنة من الصفحة للطباعة...");

                // إنشاء حاوي للطباعة بحجم A4 محسن
                var printContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي
                    Margin = new Thickness(0)
                };

                // إنشاء Border داخلي مع هوامش مناسبة
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ محتوى الصفحة الأصلية مع الحفاظ على البيانات
                var pageContent = ClonePageWithData(originalPage);
                if (pageContent != null)
                {
                    // تطبيق الاتجاه العربي على المحتوى
                    pageContent.FlowDirection = FlowDirection.RightToLeft;
                    innerBorder.Child = pageContent;
                }

                printContainer.Children.Add(innerBorder);

                // تحديث التخطيط بعناية
                printContainer.Measure(new Size(816, 1056));
                printContainer.Arrange(new Rect(0, 0, 816, 1056));
                printContainer.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة محسنة من الصفحة بنجاح");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة المحسنة: {ex.Message}");
                return CreatePrintablePage(originalPage); // العودة للطريقة العادية
            }
        }

        private Border ClonePage(Border originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة
                var clonedPage = new Border
                {
                    Width = originalPage.Width,
                    Height = originalPage.Height,
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = new Thickness(5, 10, 5, 10)
                };

                // نسخ المحتوى
                if (originalPage.Child != null)
                {
                    clonedPage.Child = originalPage.Child;
                }

                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ الصفحة: {ex.Message}");
                return originalPage;
            }
        }

        private T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }

        private FrameworkElement FindReportView(DependencyObject parent)
        {
            if (parent == null) return null;

            // البحث في العناصر الفرعية
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                // التحقق من نوع العنصر
                if (child is UserControl userControl && userControl.GetType().Name == "ReportView")
                {
                    return userControl;
                }

                // البحث المتكرر في العناصر الفرعية
                var result = FindReportView(child);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        private void ExportToPdf()
        {
            try
            {
                // البحث عن ReportView في النافذة الحالية
                var mainWindow = Application.Current.MainWindow;
                var reportView = FindReportView(mainWindow);

                if (reportView != null)
                {
                    // إنشاء مربع حوار لحفظ الملف
                    var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Title = "حفظ التقرير كملف PDF",
                        Filter = "PDF Files (*.pdf)|*.pdf",
                        DefaultExt = "pdf",
                        FileName = $"تقرير_الزيارة_{ReportData.VisitNumber}_{DateTime.Now:yyyy-MM-dd}.pdf"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        // استخدام PrintDialog مع Microsoft Print to PDF
                        var printDialog = new PrintDialog();

                        // البحث عن طابعة Microsoft Print to PDF
                        var pdfPrinter = FindPdfPrinter();
                        if (pdfPrinter != null)
                        {
                            printDialog.PrintQueue = pdfPrinter;
                        }

                        // تحديد حجم الصفحة A4
                        printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                            System.Printing.PageMediaSizeName.ISOA4);

                        // طباعة جميع الصفحات إلى PDF مع معالجة أخطاء Adobe
                        try
                        {
                            ExportReportPagesToPdf(printDialog, reportView, saveFileDialog.FileName);
                        }
                        catch (Exception pdfEx) when (pdfEx.Message.Contains("AcroDistDLL"))
                        {
                            MessageBox.Show("مشكلة في Adobe PDF!\n\n" +
                                          "يبدو أن Adobe Acrobat غير مثبت بشكل صحيح أو تالف.\n\n" +
                                          "الحلول:\n" +
                                          "1. أعد تثبيت Adobe Acrobat (النسخة الكاملة)\n" +
                                          "2. أو استخدم 'Microsoft Print to PDF' من قائمة الطابعات\n" +
                                          "3. أو استخدم زر 'تصدير PDF' من القائمة الرئيسية\n\n" +
                                          "ملاحظة: Adobe Reader وحده لا يكفي، تحتاج Adobe Acrobat الكامل",
                                          "خطأ في Adobe PDF",
                                          MessageBoxButton.OK, MessageBoxImage.Warning);
                        }

                        MessageBox.Show($"تم حفظ التقرير بنجاح!\n\n📄 تم حفظ 4 صفحات في ملف واحد:\n📁 المسار: {saveFileDialog.FileName}\n\n✅ يحتوي الملف على:\n• الصفحة 1: محضر استدراج عروض الأسعار\n• الصفحة 2: عقد إيجار السيارة (الجزء الأول)\n• الصفحة 3: عقد إيجار السيارة (الجزء الثاني)\n• الصفحة 4: بيان وإقرار", "تصدير PDF",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على التقرير للتصدير.\nتأكد من أنك في صفحة التقارير وأن التقرير معروض.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private System.Printing.PrintQueue FindPdfPrinter()
        {
            try
            {
                var printServer = new System.Printing.LocalPrintServer();
                var printQueues = printServer.GetPrintQueues();

                // البحث عن طابعة Microsoft Print to PDF
                foreach (var queue in printQueues)
                {
                    if (queue.Name.Contains("Microsoft Print to PDF") ||
                        queue.Name.Contains("PDF") ||
                        queue.Name.ToLower().Contains("pdf"))
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على طابعة PDF: {queue.Name}");
                        return queue;
                    }
                }

                System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على طابعة PDF");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن طابعة PDF: {ex.Message}");
                return null;
            }
        }

        private void ExportReportPagesToPdf(PrintDialog printDialog, FrameworkElement reportView, string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 بدء عملية تصدير الصفحات إلى PDF...");

                // البحث عن جميع صفحات التقرير
                var reportPages = FindReportPages(reportView);

                if (reportPages?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"📄 تم العثور على {reportPages.Count} صفحة للتصدير");

                    // إنشاء DocumentPaginator مخصص للصفحات المتعددة
                    var paginator = new MultiPageDocumentPaginator(reportPages);

                    // تحديد حجم الصفحة A4
                    printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                        System.Printing.PageMediaSizeName.ISOA4);

                    // طباعة جميع الصفحات كمستند واحد
                    printDialog.PrintDocument(paginator, "تقرير الزيارة الميدانية - جميع الصفحات");

                    System.Diagnostics.Debug.WriteLine($"✅ تم تصدير {reportPages.Count} صفحة إلى PDF بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على صفحات منفصلة، سيتم تصدير التقرير كاملاً");
                    // في حالة عدم العثور على الصفحات، اطبع التقرير كاملاً
                    printDialog.PrintVisual(reportView, "تقرير الزيارة الميدانية - عقد إيجار السيارة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تصدير الصفحات إلى PDF: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                // في حالة الخطأ، اطبع التقرير كاملاً
                printDialog.PrintVisual(reportView, "تقرير الزيارة الميدانية - عقد إيجار السيارة");
            }
        }

        private bool CanGenerateReport()
        {
            return SelectedVisit != null;
        }

        private bool CanPrintReport()
        {
            return SelectedVisit != null && ReportData.Projects.Any();
        }

        private bool CanExportToPdf()
        {
            return SelectedVisit != null && ReportData.Projects.Any();
        }



        /// <summary>
        /// تحميل العروض من نافذة الرسائل المهنية المفتوحة
        /// </summary>
        private List<PriceOfferItem> LoadOffersFromProfessionalMessagesWindow(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 محاولة تحميل العروض من نافذة الرسائل المهنية للزيارة: {visitNumber}");

                // البحث عن نافذة الرسائل المهنية المفتوحة
                var messagesWindow = Application.Current.Windows.OfType<Views.PowerfulMessageDocumentationWindow>().FirstOrDefault();

                if (messagesWindow?.DataContext is ViewModels.ProfessionalMessagesViewModel messagesViewModel)
                {
                    // التحقق من أن الزيارة المحددة تطابق الزيارة المطلوبة
                    if (messagesViewModel.SelectedVisit?.VisitNumber == visitNumber &&
                        messagesViewModel.OffersResults?.Any() == true)
                    {
                        var priceOffers = new List<PriceOfferItem>();

                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {messagesViewModel.OffersResults.Count} عرض في نافذة الرسائل");

                        for (int i = 0; i < messagesViewModel.OffersResults.Count; i++)
                        {
                            var offerResult = messagesViewModel.OffersResults[i];

                            // استخراج المبلغ من النص
                            var amountText = offerResult.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                            decimal.TryParse(amountText, out decimal amount);

                            var priceOffer = new PriceOfferItem
                            {
                                SerialNumber = i + 1,
                                DriverName = offerResult.DriverName,
                                PhoneNumber = "غير محدد", // يمكن جلبه من قاعدة البيانات لاحقاً
                                OfferedPrice = amount,
                                Status = offerResult.Status?.Contains("فائز") == true ? "فائز" : "مرشح",
                                IsWinner = offerResult.Status?.Contains("فائز") == true || offerResult.Status?.Contains("🏆") == true
                            };

                            priceOffers.Add(priceOffer);
                            System.Diagnostics.Debug.WriteLine($"   {i + 1}. {priceOffer.DriverName}: {priceOffer.OfferedPrice:N0} ريال - {priceOffer.Status}");
                        }

                        return priceOffers;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ الزيارة المحددة في نافذة الرسائل ({messagesViewModel.SelectedVisit?.VisitNumber}) لا تطابق الزيارة المطلوبة ({visitNumber})");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على نافذة الرسائل المهنية المفتوحة");
                }

                return new List<PriceOfferItem>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض من نافذة الرسائل: {ex.Message}");
                return new List<PriceOfferItem>();
            }
        }

        /// <summary>
        /// تحميل العروض الحقيقية من قاعدة البيانات الجديدة
        /// </summary>
        private async Task<List<PriceOfferItem>> LoadRealOffersFromDatabase(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 تحميل العروض الحقيقية للزيارة: {visitNumber}");

                if (string.IsNullOrWhiteSpace(visitNumber))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ رقم الزيارة فارغ");
                    return new List<PriceOfferItem>();
                }

                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // تشخيص: فحص جميع العروض في قاعدة البيانات
                var allQuotes = await context.DriverQuotes.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"🔍 إجمالي العروض في قاعدة البيانات: {allQuotes.Count}");

                var quotesWithVisit = allQuotes.Where(q => q.Notes != null && q.Notes.Contains("Visit:")).ToList();
                System.Diagnostics.Debug.WriteLine($"🔍 العروض التي تحتوي على رقم زيارة: {quotesWithVisit.Count}");

                foreach (var quote in quotesWithVisit.Take(5))
                {
                    System.Diagnostics.Debug.WriteLine($"   - {quote.DriverName}: {quote.QuotedPrice:N0} ريال - الملاحظات: {quote.Notes}");
                }

                // جلب العروض المحفوظة من قاعدة البيانات للزيارة المحددة فقط
                var savedOffers = await offersService.GetVisitOffersByNumberAsync(visitNumber);

                if (savedOffers?.Any() == true)
                {
                    var priceOffers = new List<PriceOfferItem>();

                    // ترتيب العروض حسب السعر (الأقل أولاً)
                    var sortedOffers = savedOffers.OrderBy(o => o.ProposedAmount).ToList();

                    System.Diagnostics.Debug.WriteLine($"📊 تفاصيل العروض المجلبة للزيارة {visitNumber}:");

                    for (int i = 0; i < sortedOffers.Count; i++)
                    {
                        var offer = sortedOffers[i];

                        var priceOffer = new PriceOfferItem
                        {
                            SerialNumber = i + 1,
                            DriverName = offer.DriverName ?? "غير محدد",
                            PhoneNumber = offer.PhoneNumber ?? "غير محدد",
                            OfferedPrice = offer.ProposedAmount,
                            Status = offer.IsWinner ? "🏆 فائز" : "تم التقديم",
                            IsWinner = offer.IsWinner
                        };

                        priceOffers.Add(priceOffer);
                        System.Diagnostics.Debug.WriteLine($"   {i + 1}. {offer.DriverName} - {offer.ProposedAmount:N0} ريال - فائز: {offer.IsWinner}");
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {priceOffers.Count} عرض حقيقي للزيارة {visitNumber}");
                    return priceOffers;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم توجد عروض محفوظة للزيارة {visitNumber}");

                    // التحقق من وجود الزيارة في النظام
                    var visitExists = context.FieldVisits.Any(v => v.VisitNumber == visitNumber);
                    if (visitExists)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ الزيارة {visitNumber} موجودة في النظام لكن لا توجد عروض أسعار لها");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ الزيارة {visitNumber} غير موجودة في النظام");
                    }

                    return new List<PriceOfferItem>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض الحقيقية للزيارة {visitNumber}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return new List<PriceOfferItem>();
            }
        }

        /// <summary>
        /// جلب العروض من بيانات الزيارة المحفوظة
        /// </summary>
        private List<PriceOfferItem> LoadOffersFromVisitData(FieldVisit visit)
        {
            try
            {
                var offers = new List<PriceOfferItem>();

                // التحقق من وجود بيانات السائقين المحددين
                if (!string.IsNullOrWhiteSpace(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split('|');
                    for (int i = 0; i < driversData.Length; i++)
                    {
                        var driverInfo = driversData[i].Trim();
                        if (!string.IsNullOrWhiteSpace(driverInfo))
                        {
                            var parts = driverInfo.Split('-');
                            var driverName = parts.Length > 0 ? parts[0].Trim() : "غير محدد";

                            // محاولة استخراج السعر من النص
                            decimal price = 0;
                            if (parts.Length > 1)
                            {
                                var priceText = parts[1].Trim();
                                // إزالة الرموز والنصوص الإضافية
                                priceText = System.Text.RegularExpressions.Regex.Replace(priceText, @"[^\d.]", "");
                                decimal.TryParse(priceText, out price);
                            }

                            // إذا لم يوجد سعر في النص، جلبه من قاعدة البيانات
                            if (price == 0)
                            {
                                price = GetDriverPriceFromDatabase(driverName, visit.VisitNumber);
                            }

                            var status = "تم التقديم";
                            var isWinner = false;

                            // التحقق من حالة الفوز
                            if (driverInfo.Contains("🏆") || driverInfo.Contains("فائز") || i == 0)
                            {
                                status = "🏆 فائز";
                                isWinner = true;
                            }

                            offers.Add(new PriceOfferItem
                            {
                                SerialNumber = i + 1,
                                DriverName = driverName,
                                PhoneNumber = GetDriverPhoneFromDatabase(driverName),
                                OfferedPrice = price,
                                Status = status,
                                IsWinner = isWinner
                            });
                        }
                    }
                }

                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب العروض من بيانات الزيارة: {ex.Message}");
                return new List<PriceOfferItem>();
            }
        }

        /// <summary>
        /// جلب رقم تلفون السائق من قاعدة البيانات
        /// </summary>
        private string GetDriverPhoneFromDatabase(string driverName)
        {
            try
            {
                using var context = new Data.ApplicationDbContext();
                var driver = context.Drivers.FirstOrDefault(d => d.Name == driverName);
                return driver?.PhoneNumber ?? "غير محدد";
            }
            catch
            {
                return "غير محدد";
            }
        }

        /// <summary>
        /// جلب سعر السائق من قاعدة البيانات
        /// </summary>
        private decimal GetDriverPriceFromDatabase(string driverName, string visitNumber)
        {
            try
            {
                using var context = new Data.ApplicationDbContext();

                // البحث في جدول عروض الأسعار أولاً
                var quote = context.DriverQuotes
                    .Where(q => q.DriverName == driverName && q.Notes.Contains(visitNumber))
                    .OrderByDescending(q => q.QuoteDate)
                    .FirstOrDefault();

                if (quote != null && quote.QuotedPrice > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"💰 Found price for {driverName}: {quote.QuotedPrice:N0}");
                    return quote.QuotedPrice;
                }

                // البحث في جدول السائقين عن سعر افتراضي
                var driver = context.Drivers.FirstOrDefault(d => d.Name == driverName);
                if (driver != null)
                {
                    // يمكن إضافة حقل DefaultPrice في جدول السائقين مستقبلاً
                    // return driver.DefaultPrice ?? 0;

                    // حالياً، إرجاع سعر تقديري بناءً على نوع المركبة
                    var estimatedPrice = EstimatePriceByVehicleType(driver.VehicleType);
                    if (estimatedPrice > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"💰 Estimated price for {driverName}: {estimatedPrice:N0}");
                        return estimatedPrice;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"⚠️ No price found for driver: {driverName}");
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب سعر السائق: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// تقدير السعر بناءً على نوع المركبة
        /// </summary>
        private decimal EstimatePriceByVehicleType(string vehicleType)
        {
            if (string.IsNullOrWhiteSpace(vehicleType))
                return 0;

            var type = vehicleType.ToLower();

            // أسعار تقديرية بناءً على نوع المركبة
            if (type.Contains("هايلكس") || type.Contains("hilux"))
                return 50000;
            else if (type.Contains("باترول") || type.Contains("patrol"))
                return 55000;
            else if (type.Contains("رينجر") || type.Contains("ranger"))
                return 48000;
            else if (type.Contains("كولورادو") || type.Contains("colorado"))
                return 52000;
            else if (type.Contains("ديماكس") || type.Contains("dmax"))
                return 47000;
            else if (type.Contains("نافارا") || type.Contains("navara"))
                return 49000;
            else
                return 45000; // سعر افتراضي
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للعروض
        /// </summary>
        private async Task CreateSamplePriceOffers(FieldVisit visit)
        {
            try
            {
                // جلب بعض السائقين من قاعدة البيانات لإنشاء عروض تجريبية
                var drivers = await _databaseService.GetDriversAsync();
                var sampleDrivers = drivers.Take(5).ToList();

                if (sampleDrivers.Any())
                {
                    var random = new Random();
                    for (int i = 0; i < sampleDrivers.Count; i++)
                    {
                        var driver = sampleDrivers[i];

                        // حساب السعر بناءً على نوع المركبة
                        var basePrice = EstimatePriceByVehicleType(driver.VehicleType);
                        if (basePrice == 0) basePrice = 45000; // سعر افتراضي

                        // إضافة تنويع عشوائي للسعر
                        var priceVariation = random.Next(-8000, 8000);
                        var finalPrice = basePrice + priceVariation;

                        // التأكد من أن السعر ليس سالباً
                        if (finalPrice < 30000) finalPrice = 30000;

                        var status = "تم التقديم";
                        var isWinner = false;

                        // تحديد الفائز (أقل سعر أو أول سائق)
                        if (i == 0)
                        {
                            status = "🏆 مقبول";
                            isWinner = true;
                        }
                        else if (i == sampleDrivers.Count - 1)
                        {
                            status = "❌ مرفوض";
                        }

                        ReportData.PriceOffers.Add(new PriceOfferItem
                        {
                            SerialNumber = i + 1,
                            DriverName = driver.Name,
                            PhoneNumber = driver.PhoneNumber ?? "غير محدد",
                            OfferedPrice = finalPrice,
                            Status = status,
                            IsWinner = isWinner
                        });
                    }

                    // ترتيب العروض حسب السعر (الأقل أولاً)
                    var sortedOffers = ReportData.PriceOffers.OrderBy(o => o.OfferedPrice).ToList();
                    ReportData.PriceOffers.Clear();

                    for (int i = 0; i < sortedOffers.Count; i++)
                    {
                        var offer = sortedOffers[i];
                        offer.SerialNumber = i + 1;

                        // تحديث حالة الفوز بناءً على الترتيب الجديد
                        if (i == 0)
                        {
                            offer.Status = "🏆 مقبول";
                            offer.IsWinner = true;
                        }
                        else
                        {
                            offer.Status = "تم التقديم";
                            offer.IsWinner = false;
                        }

                        ReportData.PriceOffers.Add(offer);
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {sampleDrivers.Count} عرض سعر تجريبي مرتب حسب السعر");
                }
                else
                {
                    // إذا لم توجد سائقين، أضف رسالة توضيحية
                    ReportData.PriceOffers.Add(new PriceOfferItem
                    {
                        SerialNumber = 1,
                        DriverName = "لا توجد عروض أسعار",
                        PhoneNumber = "غير محدد",
                        OfferedPrice = 0,
                        Status = "لم يتم تقديم عروض لهذه الزيارة",
                        IsWinner = false
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء العروض التجريبية: {ex.Message}");

                ReportData.PriceOffers.Add(new PriceOfferItem
                {
                    SerialNumber = 1,
                    DriverName = "خطأ في النظام",
                    PhoneNumber = "غير محدد",
                    OfferedPrice = 0,
                    Status = "خطأ",
                    IsWinner = false
                });
            }
        }

        /// <summary>
        /// تحديد زيارة محددة وتحميل بياناتها
        /// </summary>
        /// <param name="visit">الزيارة المحددة</param>
        public async void SetSelectedVisit(FieldVisit visit)
        {
            try
            {
                if (visit == null) return;

                System.Diagnostics.Debug.WriteLine($"🎯 SetSelectedVisit called with visit: {visit.VisitNumber}");

                // تحديد الزيارة
                SelectedVisit = visit;

                // تحميل جميع الزيارات أولاً
                LoadData();

                // انتظار قليل للتأكد من تحميل البيانات
                await Task.Delay(500);

                // البحث عن الزيارة في القائمة وتحديدها
                var foundVisit = FieldVisits.FirstOrDefault(v => v.VisitNumber == visit.VisitNumber);
                if (foundVisit != null)
                {
                    SelectedVisit = foundVisit;
                    System.Diagnostics.Debug.WriteLine($"✅ Found and selected visit: {foundVisit.VisitNumber}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ Visit {visit.VisitNumber} not found in loaded visits");
                    // إذا لم توجد الزيارة في القائمة، استخدم الزيارة الممررة مباشرة
                    SelectedVisit = visit;
                }

                // تحميل بيانات التقرير للزيارة المحددة
                GenerateReport();

                // تشخيص البيانات بعد التحميل
                System.Diagnostics.Debug.WriteLine($"📊 عدد المشاريع بعد التحميل: {ReportData?.Projects?.Count ?? 0}");
                if (ReportData?.Projects?.Any() == true)
                {
                    for (int i = 0; i < Math.Min(3, ReportData.Projects.Count); i++)
                    {
                        var proj = ReportData.Projects[i];
                        System.Diagnostics.Debug.WriteLine($"📋 مشروع {i + 1}: {proj.ProjectNumber} - {proj.ProjectName}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع في ReportData بعد التحميل!");
                }

                // إجبار تحديث الواجهة
                RaisePropertyChanged(nameof(ReportData));

                System.Diagnostics.Debug.WriteLine($"✅ Report generated for visit: {visit.VisitNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في SetSelectedVisit: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات الزيارة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات السائق الفائز الكاملة
        /// </summary>
        private async Task LoadWinnerDriverData(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🏆 بدء تحميل بيانات السائق الفائز للزيارة: {visit.VisitNumber}");

                // إنشاء كائن جديد لبيانات السائق الفائز
                var winnerDriver = new WinnerDriverInfo();

                // البحث عن السائق الفائز من عروض الأسعار
                var winnerOffer = ReportData.PriceOffers.FirstOrDefault(o => o.IsWinner);
                if (winnerOffer == null)
                {
                    // إذا لم يوجد فائز محدد، اختر أقل سعر
                    winnerOffer = ReportData.PriceOffers.OrderBy(o => o.OfferedPrice).FirstOrDefault();
                }

                if (winnerOffer != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🏆 تم العثور على السائق الفائز: {winnerOffer.DriverName}");

                    // تحديث البيانات من عرض السعر
                    winnerDriver.UpdateFromPriceOffer(winnerOffer);

                    // جلب بيانات السائق الكاملة من جدول السائقين
                    System.Diagnostics.Debug.WriteLine($"🔍 جلب بيانات السائق '{winnerOffer.DriverName}' من جدول السائقين...");
                    var driverDetails = await GetDriverWithFullDetails(winnerOffer.DriverName);

                    if (driverDetails != null)
                    {
                        winnerDriver.UpdateFromDriver(driverDetails);
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحديث بيانات السائق الفائز من قاعدة البيانات");

                        // تحديث البيانات الإضافية
                        winnerDriver.DriverName = driverDetails.Name;
                        winnerDriver.PhoneNumber = driverDetails.PhoneNumber ?? "غير محدد";
                        winnerDriver.VehicleNumber = driverDetails.VehicleNumber ?? "غير محدد";
                        winnerDriver.VehicleType = driverDetails.VehicleType ?? "غير محدد";
                        winnerDriver.VehicleColor = driverDetails.VehicleColor ?? "غير محدد";

                        // إضافة بيانات افتراضية للحقول المفقودة
                        if (string.IsNullOrWhiteSpace(winnerDriver.NationalId))
                            winnerDriver.NationalId = "يرجى إدخال رقم البطاقة";
                        if (string.IsNullOrWhiteSpace(winnerDriver.IssueDate))
                            winnerDriver.IssueDate = "يرجى إدخال تاريخ الإصدار";
                        if (string.IsNullOrWhiteSpace(winnerDriver.IssueLocation))
                            winnerDriver.IssueLocation = "يرجى إدخال مكان الإصدار";
                        if (string.IsNullOrWhiteSpace(winnerDriver.DriverRank))
                            winnerDriver.DriverRank = "سائق";
                        if (string.IsNullOrWhiteSpace(winnerDriver.ManufactureYear))
                            winnerDriver.ManufactureYear = "غير محدد";
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على بيانات السائق '{winnerOffer.DriverName}' في جدول السائقين");

                        // استخدام البيانات المتوفرة من العرض فقط
                        winnerDriver.DriverName = winnerOffer.DriverName;
                        winnerDriver.PhoneNumber = winnerOffer.PhoneNumber ?? "غير محدد";
                        winnerDriver.NationalId = "غير متوفر في النظام";
                        winnerDriver.IssueDate = "غير متوفر";
                        winnerDriver.IssueLocation = "غير متوفر";
                        winnerDriver.DriverRank = "سائق";
                        winnerDriver.VehicleNumber = "غير محدد";
                        winnerDriver.VehicleType = "غير محدد";
                        winnerDriver.VehicleCapacity = "غير محدد";
                        winnerDriver.VehicleColor = "غير محدد";
                        winnerDriver.ManufactureYear = "غير محدد";
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على سائق فائز للزيارة: {visit.VisitNumber}");

                    // محاولة جلب البيانات من السائق المتعاقد معه
                    if (!string.IsNullOrWhiteSpace(visit.DriverContract))
                    {
                        var contractedDriver = await GetDriverDetailsByName(visit.DriverContract);
                        if (contractedDriver != null)
                        {
                            winnerDriver.UpdateFromDriver(contractedDriver);
                            winnerDriver.WinningPrice = 50000; // سعر افتراضي
                            winnerDriver.ContractStatus = "متعاقد";
                            System.Diagnostics.Debug.WriteLine($"✅ تم استخدام بيانات السائق المتعاقد: {contractedDriver.Name}");
                        }
                    }
                }

                // تحديث البيانات في النموذج
                ReportData.WinnerDriver = winnerDriver;

                // تحديث النص العربي للمبلغ
                ReportData.UpdateWinnerPriceText();

                // اختبار دالة التحويل (للتطوير)
                Helpers.NumberToArabicTextHelper.TestConversion();

                // تحديث النصوص المعالجة بعد تحديث بيانات السائق الفائز
                UpdateProcessedTexts();

                System.Diagnostics.Debug.WriteLine($"🏆 ملخص بيانات السائق الفائز: {winnerDriver.GetDataSummary()}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات السائق الفائز: {ex.Message}");

                // إنشاء بيانات افتراضية في حالة الخطأ
                ReportData.WinnerDriver = new WinnerDriverInfo
                {
                    DriverName = "خطأ في تحميل البيانات",
                    PhoneNumber = "غير متوفر",
                    VehicleType = "غير محدد",
                    WinningPrice = 0,
                    ContractStatus = "خطأ"
                };
            }
        }

        /// <summary>
        /// جلب تفاصيل السائق بالاسم من جدول السائقين
        /// </summary>
        private async Task<Driver> GetDriverDetailsByName(string driverName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(driverName))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ اسم السائق فارغ");
                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن السائق: {driverName}");

                using var context = new Data.ApplicationDbContext();

                // البحث بالاسم الكامل أولاً (دقيق)
                var driver = context.Drivers
                    .FirstOrDefault(d => d.Name.Trim() == driverName.Trim());

                if (driver != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق بالاسم الكامل: {driver.Name}");
                    System.Diagnostics.Debug.WriteLine($"📋 جميع البيانات المجلبة:");
                    System.Diagnostics.Debug.WriteLine($"   📝 الاسم: {driver.Name ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🆔 رقم البطاقة: {driver.CardNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   📱 رقم التلفون: {driver.PhoneNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🚗 نوع السيارة: {driver.VehicleType ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🔢 رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🎨 لون السيارة: {driver.VehicleColor ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   📅 موديل السيارة: {driver.VehicleModel ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   ⚖️ قدرة السيارة: {driver.VehicleCapacity ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   📄 رقم الرخصة: {driver.LicenseNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   📆 تاريخ إصدار البطاقة: {(driver.CardIssueDate != default ? driver.CardIssueDate.ToString("dd/MM/yyyy") : "غير محدد")}");
                    System.Diagnostics.Debug.WriteLine($"   🏢 القطاع: {driver.SectorName ?? "غير محدد"}");
                    return driver;
                }

                // البحث بالاسم الجزئي (مرن)
                driver = context.Drivers
                    .FirstOrDefault(d => d.Name.Contains(driverName) || driverName.Contains(d.Name));

                if (driver != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق بالبحث الجزئي: {driver.Name}");
                    System.Diagnostics.Debug.WriteLine($"📱 رقم التلفون: {driver.PhoneNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"🚗 نوع السيارة: {driver.VehicleType ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"🔢 رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}");
                    return driver;
                }

                // البحث بالكلمات المفتاحية
                var keywords = driverName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (keywords.Length > 1)
                {
                    foreach (var keyword in keywords)
                    {
                        if (keyword.Length > 2) // تجاهل الكلمات القصيرة
                        {
                            driver = context.Drivers
                                .FirstOrDefault(d => d.Name.Contains(keyword));

                            if (driver != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق بالكلمة المفتاحية '{keyword}': {driver.Name}");
                                System.Diagnostics.Debug.WriteLine($"📱 رقم التلفون: {driver.PhoneNumber ?? "غير محدد"}");
                                System.Diagnostics.Debug.WriteLine($"🚗 نوع السيارة: {driver.VehicleType ?? "غير محدد"}");
                                System.Diagnostics.Debug.WriteLine($"🔢 رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}");
                                return driver;
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على السائق: {driverName}");

                // عرض قائمة بجميع السائقين المتاحين للمساعدة في التشخيص
                var allDrivers = context.Drivers.Select(d => d.Name).ToList();
                System.Diagnostics.Debug.WriteLine($"📋 السائقين المتاحين في النظام ({allDrivers.Count}):");
                foreach (var name in allDrivers.Take(10)) // عرض أول 10 فقط
                {
                    System.Diagnostics.Debug.WriteLine($"   - {name}");
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب تفاصيل السائق '{driverName}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// طريقة مساعدة لجلب بيانات السائق مع معلومات مفصلة
        /// </summary>
        public async Task<Driver> GetDriverWithFullDetails(string driverName)
        {
            var driver = await GetDriverDetailsByName(driverName);

            if (driver != null)
            {
                System.Diagnostics.Debug.WriteLine($"🎯 بيانات السائق الكاملة:");
                System.Diagnostics.Debug.WriteLine($"   الاسم: {driver.Name}");
                System.Diagnostics.Debug.WriteLine($"   رقم التلفون: {driver.PhoneNumber ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   كود السائق: {driver.DriverCode ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   نوع السيارة: {driver.VehicleType ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   لون السيارة: {driver.VehicleColor ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   القطاع: {driver.SectorName ?? "غير محدد"}");
                System.Diagnostics.Debug.WriteLine($"   الحالة: {(driver.IsActive ? "نشط" : "غير نشط")}");
            }

            return driver;
        }

        /// <summary>
        /// مزامنة بيانات السائقين بين الجداول
        /// </summary>
        private async Task SynchronizeDriverDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء مزامنة بيانات السائقين...");

                var success = await _driverDataService.SynchronizeDriverDataAsync();

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم مزامنة بيانات السائقين بنجاح");

                    // إعادة تحميل البيانات إذا كانت هناك زيارة محددة
                    if (SelectedVisit != null)
                    {
                        await LoadDriversAndPricesData(SelectedVisit);
                        await LoadWinnerDriverData(SelectedVisit);
                    }

                    MessageBox.Show("تم مزامنة بيانات السائقين بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في مزامنة البيانات");
                    MessageBox.Show("فشل في مزامنة البيانات", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في المزامنة: {ex.Message}");
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طريقة اختبار لجلب بيانات السائق (يمكن استدعاؤها من الواجهة)
        /// </summary>
        public async Task TestGetDriverData(string driverName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🧪 اختبار جلب بيانات السائق: {driverName}");

                var driver = await GetDriverWithFullDetails(driverName);

                if (driver != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ نجح الاختبار - تم جلب البيانات:");
                    System.Diagnostics.Debug.WriteLine($"   📝 الاسم الكامل: {driver.Name}");
                    System.Diagnostics.Debug.WriteLine($"   📱 رقم التلفون: {driver.PhoneNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🚗 نوع السيارة: {driver.VehicleType ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🔢 رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🎨 لون السيارة: {driver.VehicleColor ?? "غير محدد"}");
                    System.Diagnostics.Debug.WriteLine($"   🏢 القطاع: {driver.SectorName ?? "غير محدد"}");

                    // تحديث بيانات السائق الفائز للاختبار
                    var testWinner = new WinnerDriverInfo();
                    testWinner.UpdateFromDriver(driver);

                    System.Diagnostics.Debug.WriteLine($"🏆 بيانات السائق الفائز المحدثة:");
                    System.Diagnostics.Debug.WriteLine($"   {testWinner.GetDataSummary()}");

                    MessageBox.Show($"✅ تم جلب بيانات السائق بنجاح!\n\n" +
                                  $"الاسم: {driver.Name}\n" +
                                  $"التلفون: {driver.PhoneNumber ?? "غير محدد"}\n" +
                                  $"نوع السيارة: {driver.VehicleType ?? "غير محدد"}\n" +
                                  $"رقم السيارة: {driver.VehicleNumber ?? "غير محدد"}",
                                  "نجح الاختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل الاختبار - لم يتم العثور على السائق");
                    MessageBox.Show($"❌ لم يتم العثور على السائق '{driverName}' في قاعدة البيانات",
                                  "فشل الاختبار", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في اختبار جلب البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadDocumentationImages(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖼️ بدء تحميل صور التوثيق للزيارة: {visit.VisitNumber}");

                using var context = new Data.ApplicationDbContext();
                var documentation = await context.MessageDocumentations
                    .FirstOrDefaultAsync(d => d.VisitNumber == visit.VisitNumber);

                if (documentation != null)
                {
                    // تحميل مسارات الصور
                    ReportData.DocumentationImage1 = GetValidImagePath(documentation.ImagePath1);
                    ReportData.DocumentationImage2 = GetValidImagePath(documentation.ImagePath2);
                    ReportData.DocumentationImage3 = GetValidImagePath(documentation.ImagePath3);
                    ReportData.DocumentationImage4 = GetValidImagePath(documentation.ImagePath4);

                    var loadedImagesCount = new[] { ReportData.DocumentationImage1, ReportData.DocumentationImage2,
                                                   ReportData.DocumentationImage3, ReportData.DocumentationImage4 }
                                                   .Count(img => !string.IsNullOrEmpty(img));

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {loadedImagesCount} صورة توثيق للزيارة {visit.VisitNumber}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد بيانات توثيق للزيارة {visit.VisitNumber}");
                    // مسح مسارات الصور
                    ReportData.DocumentationImage1 = string.Empty;
                    ReportData.DocumentationImage2 = string.Empty;
                    ReportData.DocumentationImage3 = string.Empty;
                    ReportData.DocumentationImage4 = string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل صور التوثيق: {ex.Message}");
                // مسح مسارات الصور في حالة الخطأ
                ReportData.DocumentationImage1 = string.Empty;
                ReportData.DocumentationImage2 = string.Empty;
                ReportData.DocumentationImage3 = string.Empty;
                ReportData.DocumentationImage4 = string.Empty;
            }
        }

        private string GetValidImagePath(string imagePath)
        {
            if (string.IsNullOrEmpty(imagePath))
                return string.Empty;

            try
            {
                // التحقق من وجود الملف
                if (System.IO.File.Exists(imagePath))
                {
                    return imagePath;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ الصورة غير موجودة: {imagePath}");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من مسار الصورة {imagePath}: {ex.Message}");
                return string.Empty;
            }
        }



        /// <summary>
        /// نسخ محتوى الصفحة مع الحفاظ على البيانات
        /// </summary>
        private FrameworkElement ClonePageWithData(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 نسخ محتوى الصفحة مع البيانات...");

                // إنشاء Border جديد بنفس خصائص الأصلي
                var clonedBorder = new Border
                {
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = originalPage.Margin,
                    HorizontalAlignment = originalPage.HorizontalAlignment,
                    VerticalAlignment = originalPage.VerticalAlignment,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ المحتوى الداخلي
                if (originalPage.Child != null && originalPage.Child is FrameworkElement childElement)
                {
                    var clonedContent = CloneElement(childElement);
                    clonedBorder.Child = clonedContent;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ محتوى الصفحة بنجاح");
                return clonedBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ محتوى الصفحة: {ex.Message}");
                return originalPage; // إرجاع الأصلي في حالة الخطأ
            }
        }



        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق
        /// </summary>
        private FrameworkElement CloneElement(FrameworkElement original)
        {
            try
            {
                // التعامل مع أنواع مختلفة من العناصر
                switch (original)
                {
                    case Grid grid:
                        return CloneGrid(grid);
                    case StackPanel stackPanel:
                        return CloneStackPanel(stackPanel);
                    case TextBlock textBlock:
                        return CloneTextBlock(textBlock);
                    case Border border:
                        return CloneBorder(border);
                    default:
                        // للعناصر الأخرى، استخدم الطريقة العامة
                        return ClonePage(original as Border) ?? original;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر: {ex.Message}");
                return original;
            }
        }

        private Grid CloneGrid(Grid original)
        {
            var cloned = new Grid
            {
                Background = original.Background,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            // نسخ تعريفات الصفوف والأعمدة
            foreach (var rowDef in original.RowDefinitions)
            {
                cloned.RowDefinitions.Add(new RowDefinition { Height = rowDef.Height });
            }
            foreach (var colDef in original.ColumnDefinitions)
            {
                cloned.ColumnDefinitions.Add(new ColumnDefinition { Width = colDef.Width });
            }

            // نسخ العناصر الفرعية
            foreach (FrameworkElement child in original.Children)
            {
                var clonedChild = CloneElement(child);
                Grid.SetRow(clonedChild, Grid.GetRow(child));
                Grid.SetColumn(clonedChild, Grid.GetColumn(child));
                Grid.SetRowSpan(clonedChild, Grid.GetRowSpan(child));
                Grid.SetColumnSpan(clonedChild, Grid.GetColumnSpan(child));
                cloned.Children.Add(clonedChild);
            }

            return cloned;
        }

        private StackPanel CloneStackPanel(StackPanel original)
        {
            var cloned = new StackPanel
            {
                Background = original.Background,
                Margin = original.Margin,
                Orientation = original.Orientation,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            foreach (FrameworkElement child in original.Children)
            {
                cloned.Children.Add(CloneElement(child));
            }

            return cloned;
        }

        private TextBlock CloneTextBlock(TextBlock original)
        {
            return new TextBlock
            {
                Text = original.Text,
                FontFamily = original.FontFamily,
                FontSize = original.FontSize,
                FontWeight = original.FontWeight,
                Foreground = original.Foreground,
                Background = original.Background,
                Margin = original.Margin,
                Padding = original.Padding,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                TextAlignment = original.TextAlignment,
                TextWrapping = original.TextWrapping,
                FlowDirection = FlowDirection.RightToLeft
            };
        }

        private Border CloneBorder(Border original)
        {
            var cloned = new Border
            {
                Background = original.Background,
                BorderBrush = original.BorderBrush,
                BorderThickness = original.BorderThickness,
                CornerRadius = original.CornerRadius,
                Padding = original.Padding,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            if (original.Child != null && original.Child is FrameworkElement childElement)
            {
                cloned.Child = CloneElement(childElement);
            }

            return cloned;
        }

        /// <summary>
        /// فحص وجود Adobe Acrobat في النظام
        /// </summary>
        private void CheckAdobeAcrobatInstallation()
        {
            try
            {
                // فحص وجود Adobe Acrobat في Registry
                var adobeInstalled = false;
                var adobeVersion = "";

                // فحص مجلدات Adobe في Program Files
                var programFiles = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles);
                var programFilesX86 = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);

                var adobePaths = new[]
                {
                    Path.Combine(programFiles, "Adobe", "Acrobat DC"),
                    Path.Combine(programFiles, "Adobe", "Acrobat 2020"),
                    Path.Combine(programFiles, "Adobe", "Acrobat 2017"),
                    Path.Combine(programFilesX86, "Adobe", "Acrobat DC"),
                    Path.Combine(programFilesX86, "Adobe", "Acrobat 2020"),
                    Path.Combine(programFilesX86, "Adobe", "Acrobat 2017")
                };

                foreach (var path in adobePaths)
                {
                    if (Directory.Exists(path))
                    {
                        adobeInstalled = true;
                        adobeVersion = Path.GetFileName(path);
                        break;
                    }
                }

                // فحص وجود ملف AcroDistDLL.dll
                var acroDistFound = false;
                if (adobeInstalled)
                {
                    foreach (var path in adobePaths)
                    {
                        if (Directory.Exists(path))
                        {
                            var dllPath = Path.Combine(path, "Acrobat", "AcroDistDLL.dll");
                            if (File.Exists(dllPath))
                            {
                                acroDistFound = true;
                                break;
                            }
                        }
                    }
                }

                // طباعة النتائج
                if (adobeInstalled)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Adobe Acrobat موجود: {adobeVersion}");
                    if (acroDistFound)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ AcroDistDLL.dll موجود - يمكن استخدام Adobe PDF");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ AcroDistDLL.dll غير موجود - قد تحدث مشاكل مع Adobe PDF");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Adobe Acrobat غير مثبت");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص Adobe Acrobat: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// تحديث النصوص المتغيرة بناءً على البيانات (طريقة آمنة)
        /// </summary>
        private void UpdateDynamicTexts()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("✅ بدء تحديث النصوص المتغيرة");

                // تحديث نص مرافقة القائمين بالزيارة
                UpdateVisitConductorText();

                // تحديث نص المدة الإيجارية
                UpdateRentalDurationText();

                // تحديث اسم الطرف الأول
                UpdateFirstPartyName();

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث النصوص المتغيرة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateDynamicTexts: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث نص مرافقة القائمين بالزيارة
        /// </summary>
        private void UpdateVisitConductorText()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(ReportData?.VisitConductor))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد قائمين بالزيارة");
                    ReportData.VisitConductorPrefix = "لمرافقة الأخ/";
                    ReportData.VisitConductorSuffix = " أثناء تنفيذ المهام الميدانية المطلوبة منه .";
                    return;
                }

                // فحص بسيط وآمن للعدد
                bool hasMultipleConductors = ReportData.VisitConductor.Contains('&');

                string newPrefix = hasMultipleConductors ? "لمرافقة الأخوة" : "لمرافقة الأخ/";
                string newSuffix = hasMultipleConductors ? " أثناء تنفيذ المهام الميدانية المطلوبة منهم ." : " أثناء تنفيذ المهام الميدانية المطلوبة منه .";

                // تحديث الخصائص في ReportData
                ReportData.VisitConductorPrefix = newPrefix;
                ReportData.VisitConductorSuffix = newSuffix;

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث النص: {newPrefix} - عدد متعدد: {hasMultipleConductors}");

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateVisitConductorText: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث نص المدة الإيجارية حسب عدد الأيام
        /// </summary>
        private void UpdateRentalDurationText()
        {
            try
            {
                int daysCount = ReportData?.DaysCount ?? 1;

                string durationText = daysCount switch
                {
                    1 => "ولمدة يوم",
                    2 => "ولمدة يومين",
                    _ => $"ولمدة {daysCount} أيام"
                };

                ReportData.RentalDurationText = durationText;

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث نص المدة: {durationText} (عدد الأيام: {daysCount})");

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateRentalDurationText: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث اسم الطرف الأول ليكون اسم السائق الفائز
        /// </summary>
        private void UpdateFirstPartyName()
        {
            try
            {
                string firstPartyName = "الطرف الأول"; // القيمة الافتراضية

                if (!string.IsNullOrWhiteSpace(ReportData?.WinnerDriver?.DriverName))
                {
                    firstPartyName = ReportData.WinnerDriver.DriverName;
                }

                ReportData.FirstPartyName = firstPartyName;

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث اسم الطرف الأول: {firstPartyName}");

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateFirstPartyName: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث بيانات القائمين بالزيارة للاستمارة
        /// </summary>
        private void UpdateVisitorsForForm(FieldVisit visit)
        {
            try
            {
                ReportData.Visitors.Clear();

                if (visit.Visitors != null && visit.Visitors.Any())
                {
                    foreach (var visitor in visit.Visitors)
                    {
                        ReportData.Visitors.Add(new VisitorReportItem
                        {
                            Name = visitor.OfficerName ?? visitor.Name,
                            Rank = visitor.OfficerRank,
                            Code = visitor.OfficerCode
                        });
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث {ReportData.Visitors.Count} قائم بالزيارة للاستمارة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateVisitorsForForm: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث خط السير للاستمارة
        /// </summary>
        private void UpdateItineraryForForm(FieldVisit visit)
        {
            try
            {
                ReportData.Itinerary.Clear();

                if (visit.Itinerary != null && visit.Itinerary.Any())
                {
                    for (int i = 0; i < visit.Itinerary.Count; i++)
                    {
                        ReportData.Itinerary.Add(new ItineraryReportItem
                        {
                            DayNumber = i + 1,
                            Plan = visit.Itinerary[i]
                        });
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث {ReportData.Itinerary.Count} يوم في خط السير للاستمارة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في UpdateItineraryForForm: {ex.Message}");
            }
        }
    }
}
