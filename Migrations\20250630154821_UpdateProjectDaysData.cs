﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SFDSystem.Migrations
{
    /// <inheritdoc />
    public partial class UpdateProjectDaysData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // تحديث المشاريع الموجودة بقيم تجريبية لعدد الأيام
            migrationBuilder.Sql(@"
                UPDATE Projects SET ProjectDays = 5 WHERE ProjectNumber = '911-13727';
                UPDATE Projects SET ProjectDays = 3 WHERE ProjectNumber = '911-13717';
                UPDATE Projects SET ProjectDays = 4 WHERE ProjectNumber = '911-13720';
                UPDATE Projects SET ProjectDays = 6 WHERE ProjectNumber = '911-13885';
                UPDATE Projects SET ProjectDays = 2 WHERE ProjectNumber = '911-13782';
                UPDATE Projects SET ProjectDays = 7 WHERE ProjectNumber = '911-13917';
                UPDATE Projects SET ProjectDays = 1 WHERE ProjectNumber = '911-13916';
                UPDATE Projects SET ProjectDays = 3 WHERE ProjectNumber = '911-13824';
                UPDATE Projects SET ProjectDays = 4 WHERE ProjectNumber = '911-13891';
                UPDATE Projects SET ProjectDays = 5 WHERE ProjectNumber = '911-13890';
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // إعادة تعيين قيم ProjectDays إلى 0
            migrationBuilder.Sql(@"
                UPDATE Projects SET ProjectDays = 0;
            ");
        }
    }
}
